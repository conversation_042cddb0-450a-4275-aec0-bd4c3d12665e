# 腾讯云CLS日志分析指南

## 🔍 云函数日志查询步骤

### 1. 获取云函数执行日志
```bash
# 查询 resumePreviewGenerator 云函数最近的执行日志
# 时间范围：最近1小时
# 环境：zemuresume-4gjvx1wea78e3d1e
```

### 2. 关键错误信息查找

#### 微信云托管截图服务错误
- 搜索关键词：`"调用截图服务失败"` 或 `"SNAPSHOT_SERVICE_URL"`
- 检查云托管服务地址：`https://ai-resume-snapshot2-176277-5-1341667342.sh.run.tcloudbase.com`
- 常见错误：
  - 连接超时：`timeout`
  - 服务不可用：`503` 或 `502`
  - 认证失败：`401` 或 `403`

#### 云存储上传错误
- 搜索关键词：`"文件上传失败"` 或 `"uploadFile"`
- 检查权限问题：`permission denied`
- 检查存储空间：`storage quota exceeded`

#### 图片生成质量问题
- 搜索关键词：`"图片生成"` 或 `"PNG"`
- 检查图片大小：`fileSize`
- 检查生成时间：`processingTime`

### 3. 性能分析指标

#### 响应时间分析
- 总执行时间：查找 `"processingTime"` 字段
- 各阶段耗时：
  - HTML生成：`"生成简历HTML内容"`
  - 文件上传：`"上传HTML文件到云存储"`
  - 截图服务：`"调用截图服务"`
  - PNG上传：`"上传PNG文件到云存储"`

#### 成功率统计
- 成功标识：`"statusCode": 200`
- 失败标识：`"statusCode": 500` 或错误信息
- 降级机制：`"占位图片"` 或 `"placeholder"`

## 🛠️ 常见问题及解决方案

### 问题1：微信云托管服务不可用
**症状**：`调用截图服务失败: timeout` 或 `ECONNREFUSED`
**解决方案**：
1. 检查云托管服务状态
2. 验证服务URL是否正确
3. 检查网络连接
4. 考虑增加重试机制

### 问题2：图片生成超时
**症状**：响应时间 > 30秒
**解决方案**：
1. 优化HTML内容复杂度
2. 调整截图参数（分辨率、质量）
3. 增加云函数超时时间
4. 考虑异步处理

### 问题3：图片质量异常
**症状**：文件过小(<50KB)或过大(>5MB)
**解决方案**：
1. 调整图片质量参数
2. 优化HTML样式
3. 检查截图尺寸设置
4. 验证图片格式

### 问题4：云存储权限问题
**症状**：`storage permission denied`
**解决方案**：
1. 检查存储安全规则
2. 验证用户登录状态
3. 确认环境配置正确

## 📋 日志查询命令参考

### 查询最近1小时的错误日志
```
函数名：resumePreviewGenerator
时间范围：2025-07-30 10:00:00 - 2025-07-30 11:00:00
日志级别：ERROR
```

### 查询特定错误类型
```
搜索条件：
- "调用截图服务失败"
- "文件上传失败"  
- "statusCode": 500
- "timeout"
```

### 性能监控查询
```
搜索条件：
- "processingTime"
- "响应时间"
- "fileSize"
- "成功率"
```

## 🔧 修复验证步骤

1. **修复问题后重新测试**
2. **对比修复前后的日志**
3. **验证性能指标改善**
4. **确认稳定性（连续多次测试）**

## 📞 技术支持联系方式

如需进一步技术支持，请提供：
1. 具体错误信息
2. CLS日志截图
3. 测试时间点
4. 环境配置信息
