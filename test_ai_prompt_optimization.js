/**
 * 测试AI prompt优化效果
 * 测试resumeWorker云函数的AI分析结果
 */

const testResumeContent = `
张三
前端工程师
电话：13800138000
邮箱：<EMAIL>
地址：北京市朝阳区

教育背景：
北京大学 计算机科学与技术 2018.09-2022.06 本科

工作经历：
腾讯科技有限公司 高级前端工程师 2022.07-2024.12
- 负责微信小程序前端开发
- 优化页面性能，提升用户体验
- 参与技术架构设计

阿里巴巴集团 前端开发实习生 2021.06-2021.09
- 参与淘宝网页端开发
- 学习React框架应用

技能：
- 熟练掌握JavaScript、TypeScript
- 熟悉React、Vue框架
- 了解Node.js后端开发
`;

async function testAIPromptOptimization() {
  console.log('🤖 测试AI prompt优化效果...');
  
  try {
    // 调用resumeWorker云函数进行AI分析
    console.log('📡 调用resumeWorker云函数...');
    
    const result = await wx.cloud.callFunction({
      name: 'resumeWorker',
      data: {
        resumeContent: testResumeContent,
        fileName: 'test_resume.txt',
        fileType: 'text'
      }
    });
    
    console.log('📡 云函数调用结果:', result);
    
    if (result.result && result.result.success) {
      const data = result.result.data;
      
      console.log('\n📋 AI解析结果分析:');
      console.log('- 处理时间:', result.result.processingTime || 0, 'ms');
      console.log('- 使用模型:', data.model || '未知');
      
      // 分析个人信息
      if (data.personalInfo) {
        console.log('\n👤 个人信息分析:');
        console.log('- 姓名:', data.personalInfo.name || '未提取');
        console.log('- 职业title:', data.personalInfo.title || data.personalInfo.position || '未提取');
        console.log('- 联系方式:', data.personalInfo.phone || '未提取');
        console.log('- 地址:', data.personalInfo.location || data.personalInfo.address || '未提取');
        
        const hasCorrectFields = data.personalInfo.name && 
                               (data.personalInfo.title || data.personalInfo.position);
        console.log('✅ 字段提取正确:', hasCorrectFields ? '是' : '否');
      }
      
      // 分析教育背景
      if (data.education && Array.isArray(data.education)) {
        console.log('\n🎓 教育背景分析:');
        data.education.forEach((edu, index) => {
          console.log(`${index + 1}. 学校: "${edu.school || '未提取'}"`);
          console.log(`   专业: "${edu.major || edu.field || '未提取'}"`);
          console.log(`   时间: "${edu.duration || edu.period || edu.year || '未提取'}"`);
          
          const hasCorrectFields = edu.school && edu.major && 
                                 (edu.duration || edu.period || edu.year);
          console.log(`   ✅ 字段提取正确: ${hasCorrectFields ? '是' : '否'}`);
        });
      }
      
      // 分析工作经历
      if (data.workExperience && Array.isArray(data.workExperience)) {
        console.log('\n💼 工作经历分析:');
        data.workExperience.forEach((work, index) => {
          console.log(`${index + 1}. 公司: "${work.company || '未提取'}"`);
          console.log(`   职位: "${work.position || work.title || '未提取'}"`);
          console.log(`   时间: "${work.duration || work.period || '未提取'}"`);
          
          const hasCorrectFields = work.company && 
                                 (work.position || work.title) && 
                                 (work.duration || work.period);
          console.log(`   ✅ 字段提取正确: ${hasCorrectFields ? '是' : '否'}`);
        });
      }
      
      // 分析积木生成
      if (data.bricks && Array.isArray(data.bricks)) {
        console.log('\n🧱 积木生成分析:');
        console.log('- 总积木数:', data.bricks.length);
        
        const personalBricks = data.bricks.filter(b => b.category === 'personal');
        const educationBricks = data.bricks.filter(b => b.category === 'education');
        const experienceBricks = data.bricks.filter(b => b.category === 'experience');
        
        console.log('- 个人信息积木:', personalBricks.length);
        console.log('- 教育背景积木:', educationBricks.length);
        console.log('- 工作经历积木:', experienceBricks.length);
        
        // 检查积木标题格式
        personalBricks.forEach(brick => {
          console.log(`个人信息积木 - 标题: "${brick.title}", 描述: "${brick.description}"`);
        });
        
        educationBricks.forEach(brick => {
          console.log(`教育背景积木 - 标题: "${brick.title}", 描述: "${brick.description}"`);
        });
        
        experienceBricks.forEach(brick => {
          console.log(`工作经历积木 - 标题: "${brick.title}", 描述: "${brick.description}"`);
        });
      }
      
      console.log('\n✅ AI prompt优化测试完成');
      return true;
      
    } else {
      console.error('❌ 云函数调用失败:', result);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return false;
  }
}

// 运行测试
testAIPromptOptimization();
