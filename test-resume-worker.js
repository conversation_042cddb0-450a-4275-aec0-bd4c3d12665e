/**
 * 测试CloudBase resumeWorker云函数
 */

const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

async function testResumeWorker() {
  try {
    console.log('🧪 测试CloudBase resumeWorker云函数...');
    
    const result = await app.callFunction({
      name: 'resumeWorker',
      data: {
        fileId: 'cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1341667342/resumes/1753088131523-徐瑜泽-商家运营&成长.pdf',
        fileName: '徐瑜泽-商家运营&成长.pdf',
        fileType: 'pdf',
        userId: 'test-user'
      }
    });
    
    console.log('✅ 云函数调用完成');
    console.log('📊 返回结果结构:', {
      hasResult: !!result.result,
      resultType: typeof result.result,
      hasSuccess: result.result ? !!result.result.success : false,
      hasData: result.result ? !!result.result.data : false,
      hasError: result.result ? !!result.result.error : false
    });
    
    if (result.result && result.result.success) {
      console.log('🎉 简历解析成功！');
      console.log('📊 解析数据统计:');
      const data = result.result.data;
      
      if (data.personalInfo) {
        console.log('- 个人信息: ✅', {
          name: data.personalInfo.name || '未提取',
          email: data.personalInfo.email || '未提取',
          phone: data.personalInfo.phone || '未提取'
        });
      } else {
        console.log('- 个人信息: ❌');
      }
      
      if (data.workExperience && Array.isArray(data.workExperience)) {
        console.log('- 工作经历: ✅', data.workExperience.length + '个');
        data.workExperience.forEach((exp, index) => {
          console.log(`  ${index + 1}. ${exp.company || '未知公司'} - ${exp.position || '未知职位'}`);
        });
      } else {
        console.log('- 工作经历: ❌');
      }
      
      if (data.education && Array.isArray(data.education)) {
        console.log('- 教育背景: ✅', data.education.length + '个');
        data.education.forEach((edu, index) => {
          console.log(`  ${index + 1}. ${edu.school || '未知学校'} - ${edu.major || '未知专业'}`);
        });
      } else {
        console.log('- 教育背景: ❌');
      }
      
      if (data.skills) {
        console.log('- 技能: ✅', Object.keys(data.skills).length + '类');
        Object.entries(data.skills).forEach(([category, skills]) => {
          if (Array.isArray(skills) && skills.length > 0) {
            console.log(`  ${category}: ${skills.join(', ')}`);
          }
        });
      } else {
        console.log('- 技能: ❌');
      }
      
      console.log('🔧 处理时间:', result.result.processingTime + 'ms');
      console.log('🤖 AI模型:', result.result.model || '未知');
      
    } else {
      console.log('❌ 简历解析失败');
      if (result.result && result.result.error) {
        console.log('错误信息:', result.result.error);
      } else {
        console.log('未知错误，完整结果:', JSON.stringify(result, null, 2));
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
testResumeWorker().then(() => {
  console.log('🏁 测试完成');
  process.exit(0);
}).catch(error => {
  console.error('💥 测试异常:', error);
  process.exit(1);
});
