/**
 * 测试用户积木数据持久性修复效果
 * 验证修复后的用户积木数据是否具备与测试积木相同的持久性特征
 */

console.log('🧪 开始测试用户积木数据持久性修复效果...');

// 模拟用户简历分析后的积木数据
const mockUserBricks = [
  {
    id: 'user_brick_1_' + Date.now(),
    title: '用户测试积木1',
    content: '验证用户积木数据持久性',
    category: 'skill',
    tags: ['用户数据', '持久性测试'],
    createTime: new Date().toISOString(),
    source: 'resume_upload'
  },
  {
    id: 'user_brick_2_' + Date.now(),
    title: '用户测试积木2',
    content: '验证云数据库保存功能',
    category: 'project',
    tags: ['云数据库', '用户数据'],
    createTime: new Date().toISOString(),
    source: 'resume_upload'
  },
  {
    id: 'user_brick_3_' + Date.now(),
    title: '用户测试积木3',
    content: '验证数据持久化机制',
    category: 'experience',
    tags: ['数据持久化', '用户测试'],
    createTime: new Date().toISOString(),
    source: 'resume_upload'
  }
];

/**
 * 测试修复后的用户积木数据保存逻辑
 */
async function testUserBricksPersistence() {
  console.log('\n🧪 测试1: 修复后的用户积木数据保存逻辑');
  
  try {
    // 模拟获取积木管理器
    const { instance: BrickManager } = require('utils/brick-manager.js');
    
    // 确保积木管理器已初始化
    await BrickManager.init();
    console.log('✅ 积木管理器初始化成功');
    
    // 获取保存前的积木数量
    const beforeBricks = await BrickManager.getBricks();
    console.log(`📊 保存前积木数量: ${beforeBricks.length}`);
    
    // 测试批量保存用户积木数据
    console.log('🔄 开始批量保存用户积木数据...');
    const batchResult = await BrickManager.addBricksBatch(mockUserBricks);
    
    console.log('📊 批量保存结果:', {
      success: batchResult.success,
      totalCount: batchResult.totalCount,
      addedCount: batchResult.addedCount,
      cloudSaveSuccess: batchResult.cloudSaveSuccess
    });
    
    // 验证数据是否保存到云数据库
    console.log('🔄 重新从云数据库加载，验证用户积木是否保存...');
    BrickManager.initialized = false;
    BrickManager.bricks = [];
    const afterBricks = await BrickManager.getBricks();
    
    console.log(`📊 保存后积木数量: ${afterBricks.length}`);
    
    // 检查用户积木是否都保存成功
    const hasAllUserBricks = mockUserBricks.every(testBrick => 
      afterBricks.some(brick => brick.id === testBrick.id)
    );
    
    console.log(`🎯 用户积木持久性测试: ${hasAllUserBricks ? '✅ 通过' : '❌ 失败'}`);
    
    if (hasAllUserBricks) {
      console.log('✅ 所有用户积木都已成功保存到云数据库，具备持久性');
      
      // 验证具体的用户积木数据
      mockUserBricks.forEach(testBrick => {
        const savedBrick = afterBricks.find(brick => brick.id === testBrick.id);
        if (savedBrick) {
          console.log(`  ✅ 用户积木 "${testBrick.title}" 已保存，ID: ${savedBrick.id}`);
        }
      });
    } else {
      console.log('❌ 部分用户积木未能保存到云数据库');
      
      // 显示缺失的积木
      mockUserBricks.forEach(testBrick => {
        const savedBrick = afterBricks.find(brick => brick.id === testBrick.id);
        if (!savedBrick) {
          console.log(`  ❌ 用户积木 "${testBrick.title}" 未保存`);
        }
      });
    }
    
    return {
      success: hasAllUserBricks,
      beforeCount: beforeBricks.length,
      afterCount: afterBricks.length,
      addedCount: batchResult.addedCount,
      cloudSaveSuccess: batchResult.cloudSaveSuccess
    };
    
  } catch (error) {
    console.error('❌ 用户积木持久性测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试数据持久性（模拟小程序重新编译）
 */
async function testDataPersistenceAfterRecompile() {
  console.log('\n🧪 测试2: 数据持久性（模拟小程序重新编译）');
  
  try {
    // 模拟小程序重新编译：清空内存数据
    const { instance: BrickManager } = require('utils/brick-manager.js');
    
    // 清空管理器状态（模拟重新编译）
    BrickManager.initialized = false;
    BrickManager.bricks = [];
    
    // 清空全局数据（模拟重新编译）
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.bricks = [];
    }
    
    console.log('🔄 已清空内存数据，模拟小程序重新编译');
    
    // 重新加载数据
    console.log('🔄 重新加载积木数据...');
    const reloadedBricks = await BrickManager.getBricks();
    
    console.log(`📊 重新加载后积木数量: ${reloadedBricks.length}`);
    
    // 检查用户积木是否仍然存在
    const userBricksStillExist = mockUserBricks.every(testBrick => 
      reloadedBricks.some(brick => brick.id === testBrick.id)
    );
    
    console.log(`🎯 重新编译后持久性测试: ${userBricksStillExist ? '✅ 通过' : '❌ 失败'}`);
    
    if (userBricksStillExist) {
      console.log('✅ 用户积木数据在模拟重新编译后仍然存在，持久性验证成功');
    } else {
      console.log('❌ 用户积木数据在模拟重新编译后丢失，持久性验证失败');
    }
    
    return {
      success: userBricksStillExist,
      reloadedCount: reloadedBricks.length
    };
    
  } catch (error) {
    console.error('❌ 重新编译后持久性测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 对比测试积木和用户积木的持久性
 */
async function compareTestAndUserBricksPersistence() {
  console.log('\n🧪 测试3: 对比测试积木和用户积木的持久性');
  
  try {
    const { instance: BrickManager } = require('utils/brick-manager.js');
    const allBricks = await BrickManager.getBricks();
    
    // 查找测试积木
    const testBricks = allBricks.filter(brick => 
      brick.title && (
        brick.title.includes('测试积木') || 
        brick.title.includes('批量测试') ||
        brick.title.includes('最终批量')
      )
    );
    
    // 查找用户积木
    const userBricks = allBricks.filter(brick => 
      brick.title && brick.title.includes('用户测试积木')
    );
    
    console.log(`📊 测试积木数量: ${testBricks.length}`);
    console.log(`📊 用户积木数量: ${userBricks.length}`);
    
    // 显示测试积木
    if (testBricks.length > 0) {
      console.log('🔍 发现的测试积木:');
      testBricks.forEach(brick => {
        console.log(`  - ${brick.title} (ID: ${brick.id})`);
      });
    }
    
    // 显示用户积木
    if (userBricks.length > 0) {
      console.log('🔍 发现的用户积木:');
      userBricks.forEach(brick => {
        console.log(`  - ${brick.title} (ID: ${brick.id})`);
      });
    }
    
    // 验证持久性对比
    const bothTypesExist = testBricks.length > 0 && userBricks.length > 0;
    console.log(`🎯 持久性对比测试: ${bothTypesExist ? '✅ 通过' : '❌ 失败'}`);
    
    if (bothTypesExist) {
      console.log('✅ 测试积木和用户积木都具备持久性，修复成功');
    } else {
      console.log('❌ 测试积木或用户积木缺失，需要进一步检查');
    }
    
    return {
      success: bothTypesExist,
      testBricksCount: testBricks.length,
      userBricksCount: userBricks.length,
      totalBricksCount: allBricks.length
    };
    
  } catch (error) {
    console.error('❌ 持久性对比测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 执行完整的持久性测试套件
 */
async function runCompletePersistenceTest() {
  console.log('🚀 开始执行完整的用户积木持久性测试套件...\n');
  
  const results = {
    test1: null,
    test2: null,
    test3: null,
    overall: false
  };
  
  try {
    // 测试1: 修复后的保存逻辑
    results.test1 = await testUserBricksPersistence();
    
    // 测试2: 重新编译后的持久性
    results.test2 = await testDataPersistenceAfterRecompile();
    
    // 测试3: 对比测试
    results.test3 = await compareTestAndUserBricksPersistence();
    
    // 综合评估
    results.overall = results.test1.success && results.test2.success && results.test3.success;
    
    console.log('\n📋 测试结果汇总:');
    console.log(`  测试1 - 保存逻辑: ${results.test1.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`  测试2 - 重编译持久性: ${results.test2.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`  测试3 - 持久性对比: ${results.test3.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`  综合评估: ${results.overall ? '✅ 修复成功' : '❌ 需要进一步修复'}`);
    
    if (results.overall) {
      console.log('\n🎉 用户积木数据持久性修复验证成功！');
      console.log('✅ 用户积木数据现在具备与测试积木相同的持久性特征');
    } else {
      console.log('\n⚠️ 用户积木数据持久性修复需要进一步完善');
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ 完整测试套件执行失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 导出测试函数
module.exports = {
  testUserBricksPersistence,
  testDataPersistenceAfterRecompile,
  compareTestAndUserBricksPersistence,
  runCompletePersistenceTest
};

// 如果直接运行此脚本
if (require.main === module) {
  runCompletePersistenceTest().then(results => {
    console.log('\n🏁 测试完成，结果:', results);
  }).catch(error => {
    console.error('❌ 测试执行失败:', error);
  });
}
