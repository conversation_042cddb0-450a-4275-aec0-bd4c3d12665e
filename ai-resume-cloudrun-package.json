{"name": "resume-snapshot", "version": "1.0.0", "description": "AI简历系统微信云托管截图服务 - Resume snapshot service for generating PNG and PDF previews", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "docker:build": "docker build -t resume-snapshot .", "docker:run": "docker run -p 80:80 resume-snapshot", "docker:test": "docker run --rm -p 8080:80 resume-snapshot &amp; sleep 10 &amp;&amp; curl -f http://localhost:8080/health"}, "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "cors": "^2.8.5", "express": "^4.18.2", "helmet": "^7.1.0", "puppeteer": "^21.6.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["resume", "snapshot", "pdf", "png", "puppeteer", "cloudbase", "wechat", "cloudrun", "ai", "screenshot"], "author": "AI Resume System", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xuyuzeamazon/ai-resume-cloudrun.git"}, "bugs": {"url": "https://github.com/xuyuzeamazon/ai-resume-cloudrun/issues"}, "homepage": "https://github.com/xuyuzeamazon/ai-resume-cloudrun#readme"}