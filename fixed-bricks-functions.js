/**
 * 修复版本的积木页面关键函数
 * 用于解决"能力积木"页面加载卡顿问题
 * 修复了与微信小程序环境的兼容性问题
 */

// 优化版本的setFilter函数
function optimizedSetFilter(e) {
    const filter = e.currentTarget.dataset.filter

    // 性能优化：如果当前筛选条件没有变化，则不重新筛选
    if (this.data.currentFilter === filter) {
        console.log('筛选条件未变化，跳过重新筛选')
        return
    }

    // 显示加载状态
    wx.showLoading({
        title: '加载中...',
        mask: true
    })

    this.setData({ currentFilter: filter })

    // 使用setTimeout延迟执行筛选，让UI先更新
    setTimeout(() => {
        this.filterBricks()
        this.filterResumes()
        wx.hideLoading()
    }, 50)
}

// 优化版本的filterBricks函数
function optimizedFilterBricks() {
    const { bricks, currentFilter } = this.data

    // 如果没有积木数据，直接返回空数组
    if (!bricks || bricks.length === 0) {
        this.setData({ filteredBricks: [] })
        return
    }

    console.log('🔍 筛选积木 - 当前筛选条件:', currentFilter)

    // 使用缓存优化：检查是否已有此筛选条件的缓存结果
    const cacheKey = `filter_cache_${currentFilter}`
    if (this.filterCache && this.filterCache[cacheKey]) {
        console.log('🔍 使用缓存的筛选结果')
        this.setData({ filteredBricks: this.filterCache[cacheKey] })
        return
    }

    // 初始化缓存对象（如果不存在）
    if (!this.filterCache) {
        this.filterCache = {}
    }

    let filtered = bricks

    // 按分类筛选 - 优化版本使用Map进行快速分类
    if (currentFilter !== 'all' && currentFilter !== 'bricks' && currentFilter !== 'resumes') {
        // 预定义分类映射，避免重复字符串比较
        const categoryMap = {
            'personal': ['personal', '个人', '个人信息', 'personalInfo'],
            'education': ['education', '教育', '教育背景', 'educationBackground'],
            'experience': ['experience', '经验', '工作经历', 'workExperience', 'work'],
            'skills': ['skills', '技能', '技术能力', '技能证书', 'project', '项目', '项目经验'],
            'skill': ['skills', '技能', '技术能力'],
            'project': ['project', '项目', '项目经验']
        }

        // 获取当前筛选条件对应的分类数组
        const validCategories = categoryMap[currentFilter] || []

        // 一次性筛选，减少循环次数
        filtered = filtered.filter(brick => {
            const category = (brick.category || '').toLowerCase()
            const type = (brick.type || '').toLowerCase()
            return validCategories.includes(category) || validCategories.includes(type)
        })
    }

    // 按搜索关键词筛选
    if (this.data.searchKeyword) {
        const keyword = this.data.searchKeyword.toLowerCase()
        filtered = filtered.filter(brick => {
            return (
                (brick.title && brick.title.toLowerCase().includes(keyword)) ||
                (brick.description && brick.description.toLowerCase().includes(keyword)) ||
                (brick.content && brick.content.toLowerCase().includes(keyword)) ||
                (brick.keywords && brick.keywords.some(k => k.toLowerCase().includes(keyword)))
            )
        })
    }

    // 缓存筛选结果
    this.filterCache[cacheKey] = filtered

    // 更新UI
    this.setData({ filteredBricks: filtered })
}

// 修复版本的loadBricksList函数 - 使用Promise而不是async/await
function optimizedLoadBricksList() {
    console.log('开始加载积木列表')
    this.setData({ loading: true, loadingText: '加载积木中...' })

    // 使用统一的积木管理器实例
    const BrickManagerModule = require('../../utils/brick-manager.js')
    const BrickManager = BrickManagerModule.instance

    // 性能优化：添加缓存机制
    const now = Date.now()
    const cacheExpiry = this.bricksDataCacheTime ? (this.bricksDataCacheTime + 60000) : 0 // 1分钟缓存

    // 如果有缓存且未过期，直接使用缓存数据
    if (this.bricksDataCache && cacheExpiry > now) {
        console.log('✅ 使用缓存的积木数据')
        this._processBricksData(this.bricksDataCache)
        return Promise.resolve()
    }

    // 检查是否需要修复数据
    return this.checkIfNeedsDataRepair()
        .then(needsRepair => {
            if (needsRepair) {
                console.log('🧹 检测到数据需要修复，开始强制重新生成...')
                this.setData({ loadingText: '正在修复数据...' })

                // 使用强制重新生成功能
                return BrickManager.forceRegenerateDefaults()
                    .then(() => {
                        console.log('✅ 数据强制修复完成')
                        return BrickManager.getBricks()
                    })
            } else {
                // 直接获取积木数据
                return BrickManager.getBricks()
            }
        })
        .then(bricksData => {
            // 更新缓存
            this.bricksDataCache = bricksData
            this.bricksDataCacheTime = now

            console.log('✅ 通过BrickManager获取积木数据:', bricksData ? bricksData.length : 0)

            // 处理积木数据
            this._processBricksData(bricksData)
            return Promise.resolve()
        })
        .catch(error => {
            console.error('❌ 通过BrickManager加载积木数据失败:', error)
            this.setData({
                bricks: [],
                filteredBricks: [],
                loading: false
            })
            return Promise.reject(error)
        })
}

// 辅助函数：处理积木数据
function _processBricksData(bricksData) {
    if (Array.isArray(bricksData) && bricksData.length > 0) {
        // 清理"技能特长"空积木
        bricksData = bricksData.filter(brick => {
            const isSkillSpecialtyEmpty = brick.title === '技能特长' && (!brick.description || brick.description.trim() === '')
            return !isSkillSpecialtyEmpty
        })

        // 处理积木数据 - 使用批量处理减少循环次数
        const processedBricks = bricksData.map(brick => ({
            ...brick,
            selected: false,
            icon: this.getBrickIcon(brick.category),
            matchCount: brick.usageCount || 0,
            createTime: this.formatDate(brick.createTime) || '2024-01-01',
            updateTime: this.formatDate(brick.updateTime)
        }))

        // 批量更新数据，减少setData调用次数
        this.setData({
            bricks: processedBricks,
            loading: false
        })

        // 清除筛选缓存
        this.filterCache = {}

        // 更新统计和筛选
        this.updateCounts()
        this.filterBricks()

        console.log('✅ 成功通过BrickManager加载积木数据')
    } else {
        console.log('⚠️ 没有找到积木数据，显示空状态')
        this.setData({
            bricks: [],
            filteredBricks: [],
            loading: false
        })
    }
}

// 优化版本的updateCounts函数
function optimizedUpdateCounts() {
    try {
        const { bricks, resumes } = this.data

        // 使用Map进行分类计数，减少循环次数
        const counts = {
            personal: 0,
            education: 0,
            experience: 0,
            skill: 0,
            project: 0,
            ability: 0,
            total: bricks.length,
            resume: resumes ? resumes.length : 0
        }

        // 预定义分类映射
        const categoryMap = {
            'personal': ['personal', '个人', '个人信息', 'personalInfo'],
            'education': ['education', '教育', '教育背景', 'educationBackground'],
            'experience': ['experience', '经验', '工作经历', 'workExperience', 'work'],
            'skill': ['skills', '技能', '技术能力', '技能证书'],
            'project': ['project', '项目', '项目经验']
        }

        // 一次性遍历所有积木
        bricks.forEach(brick => {
            const category = (brick.category || '').toLowerCase()
            const type = (brick.type || '').toLowerCase()

            // 检查每个分类
            Object.entries(categoryMap).forEach(([countKey, validCategories]) => {
                if (validCategories.includes(category) || validCategories.includes(type)) {
                    counts[countKey]++
                }
            })
        })

        // 计算能力积木总数（技能+项目）
        counts.ability = counts.skill + counts.project

        // 批量更新数据，减少setData调用次数
        this.setData({
            personalCount: counts.personal,
            educationCount: counts.education,
            experienceCount: counts.experience,
            skillCount: counts.skill,
            projectCount: counts.project,
            abilityCount: counts.ability,
            totalCount: counts.total,
            resumeCount: counts.resume
        })

        console.log(`📊 统计更新 - 总计: ${counts.total}, 个人: ${counts.personal}, 教育: ${counts.education}, 经验: ${counts.experience}, 技能: ${counts.skill}, 项目: ${counts.project}, 能力: ${counts.ability}, 简历: ${counts.resume}`)
    } catch (error) {
        console.error('❌ 更新统计失败:', error)
    }
}

module.exports = {
    optimizedSetFilter,
    optimizedFilterBricks,
    optimizedLoadBricksList,
    optimizedUpdateCounts,
    _processBricksData
}