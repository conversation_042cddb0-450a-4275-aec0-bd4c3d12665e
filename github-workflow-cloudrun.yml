name: 微信云托管自动部署

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  CLOUDBASE_ENV_ID: ${{ secrets.CLOUDBASE_ENV_ID }}
  CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
  CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行测试
      run: npm test
      continue-on-error: true
      
    - name: 构建 Docker 镜像
      run: |
        docker build -t resume-snapshot:${{ github.sha }} .
        docker tag resume-snapshot:${{ github.sha }} resume-snapshot:latest
        
    - name: 测试 Docker 镜像
      run: |
        docker run -d --name test-container -p 8080:80 resume-snapshot:latest
        sleep 10
        curl -f http://localhost:8080/health || exit 1
        docker stop test-container
        docker rm test-container
        
    - name: 安装 CloudBase CLI
      run: |
        npm install -g @cloudbase/cli
        
    - name: 配置 CloudBase 认证
      run: |
        tcb login --apiKeyId ${{ secrets.CLOUDBASE_SECRET_ID }} --apiKey ${{ secrets.CLOUDBASE_SECRET_KEY }}
        
    - name: 部署到微信云托管
      run: |
        tcb framework deploy --envId ${{ secrets.CLOUDBASE_ENV_ID }}
        
    - name: 部署后验证
      run: |
        sleep 30
        # 这里可以添加部署后的验证逻辑
        echo "部署完成，请手动验证服务状态"
        
    - name: 通知部署结果
      if: always()
      run: |
        if [ ${{ job.status }} == 'success' ]; then
          echo "✅ 部署成功！"
        else
          echo "❌ 部署失败！"
        fi
