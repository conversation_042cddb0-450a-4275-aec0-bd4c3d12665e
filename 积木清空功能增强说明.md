# 积木清空功能增强说明

## 功能概述
原有的积木清空功能只能清空本地数据，现在已增强为可以同时清空本地和云端数据库中的所有积木数据。

## 已完成的修改

### 1. 云函数增强 ✅
- 文件：`cloudfunctions/brickManager/index.js`
- 新增了 `clearAll` 操作类型
- 新增了 `clearAllBricks` 函数，支持批量删除用户的所有积木数据
- 包含安全确认机制，需要传入 `confirm: true` 参数

### 2. 前端函数增强 📝
- 创建了增强版的 `clearAllBricks` 函数（保存在 `enhanced_clearAllBricks.js`）
- 需要手动替换到 `pages/bricks/bricks.js` 文件中

## 需要手动操作的步骤

### 步骤1：替换前端清空函数
1. 打开 `pages/bricks/bricks.js` 文件
2. 找到现有的 `clearAllBricks()` 函数
3. 将其完整替换为 `enhanced_clearAllBricks.js` 文件中的函数代码

### 步骤2：部署云函数
1. 右键点击 `cloudfunctions/brickManager` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

## 新功能特性

### 🔄 双重清空
- **云端清空**：删除数据库中用户的所有积木数据
- **本地清空**：清空页面数据、全局数据和本地存储

### 🛡️ 安全机制
- 用户确认对话框，明确提示将清空本地和云端数据
- 云函数层面的二次确认机制
- 详细的操作日志记录

### 🌐 网络容错
- 如果云端清空失败，会提示用户选择是否只清空本地数据
- 显示加载状态，提升用户体验
- 清空完成后显示具体删除的积木数量

### 📊 操作反馈
- 实时显示清空进度
- 成功后显示删除的积木数量
- 失败时提供详细的错误信息和备选方案

## 使用效果

用户点击清空按钮后：
1. 显示确认对话框："确定要清空所有积木数据吗？此操作将同时清空本地和云端数据，不可恢复。"
2. 确认后显示加载提示："正在清空..."
3. 调用云函数删除云端数据
4. 清空本地数据
5. 显示成功提示："清空成功，本地+云端共X个积木"

## 技术实现

### 云函数部分
```javascript
// 新增的clearAll操作
case 'clearAll':
  return await clearAllBricks(openid, data);

// 清空所有积木的函数
async function clearAllBricks(openid, params = {}) {
  // 安全确认
  if (!params.confirm) {
    throw new Error('清空操作需要确认参数');
  }
  
  // 批量删除用户的所有积木
  const result = await db.collection('bricks')
    .where({ _openid: openid })
    .remove();
    
  return { removed: result.stats.removed };
}
```

### 前端部分
```javascript
// 调用云函数清空云端数据
wx.cloud.callFunction({
  name: 'brickManager',
  data: {
    action: 'clearAll',
    data: { confirm: true }
  }
}).then(result => {
  // 解析结果并清空本地数据
  const removedCount = JSON.parse(result.result.body).data.removed;
  // ... 清空本地数据逻辑
});
```

## 注意事项
- 此操作不可恢复，请谨慎使用
- 确保网络连接正常，以保证云端数据能够正确清空
- 如果只需要清空本地数据，建议在网络异常时选择"仅清空本地"选项