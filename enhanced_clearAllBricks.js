// 增强版清空所有积木函数 - 支持清除云端数据
// 请将此函数替换pages/bricks/bricks.js中的clearAllBricks函数

clearAllBricks() {
    wx.showModal({
        title: '确认清空',
        content: '确定要清空所有积木数据吗？此操作将同时清空本地和云端数据，不可恢复。',
        confirmText: '确认清空',
        cancelText: '取消',
        confirmColor: '#ff4757',
        success: (res) => {
            if (res.confirm) {
                // 显示加载提示
                wx.showLoading({
                    title: '正在清空...',
                    mask: true
                });

                // 调用云函数清空云端数据
                wx.cloud.callFunction({
                    name: 'brickManager',
                    data: {
                        action: 'clearAll',
                        data: {
                            confirm: true
                        }
                    }
                }).then(result => {
                    console.log('☁️ 云端清空结果:', result);

                    // 清空本地数据
                    try {
                        // 清空页面数据
                        this.setData({
                            bricks: [],
                            skillCount: 0,
                            projectCount: 0,
                            abilityCount: 0,
                            personalCount: 0,
                            educationCount: 0,
                            experienceCount: 0,
                            selectedBricks: []
                        });

                        // 通过状态管理器清空数据
                        const app = getApp();
                        if (app.stateManager && app.stateManager.clearBricks) {
                            app.stateManager.clearBricks();
                        } else {
                            // 兼容处理：直接清空全局数据和本地存储
                            app.globalData.bricks = [];
                            wx.removeStorageSync('bricks');
                        }

                        wx.hideLoading();

                        // 解析云函数返回结果
                        const cloudResult = JSON.parse(result.result.body);
                        const removedCount = cloudResult.data.removed || 0;

                        wx.showToast({
                            title: `清空成功\n本地+云端共${removedCount}个积木`,
                            icon: 'success',
                            duration: 2000
                        });

                        console.log(`✅ 积木数据清空完成 - 云端删除: ${removedCount}个`);

                    } catch (error) {
                        console.error('❌ 本地数据清空失败:', error);
                        wx.hideLoading();
                        wx.showToast({
                            title: '本地清空失败',
                            icon: 'error'
                        });
                    }

                }).catch(error => {
                    console.error('❌ 云端清空失败:', error);
                    wx.hideLoading();

                    // 云端清空失败，询问是否只清空本地数据
                    wx.showModal({
                        title: '云端清空失败',
                        content: '无法连接到云端服务，是否只清空本地数据？',
                        confirmText: '仅清空本地',
                        cancelText: '取消',
                        success: (modalRes) => {
                            if (modalRes.confirm) {
                                try {
                                    // 清空页面数据
                                    this.setData({
                                        bricks: [],
                                        skillCount: 0,
                                        projectCount: 0,
                                        abilityCount: 0,
                                        personalCount: 0,
                                        educationCount: 0,
                                        experienceCount: 0,
                                        selectedBricks: []
                                    });

                                    // 通过状态管理器清空数据
                                    const app = getApp();
                                    if (app.stateManager && app.stateManager.clearBricks) {
                                        app.stateManager.clearBricks();
                                    } else {
                                        // 兼容处理：直接清空全局数据和本地存储
                                        app.globalData.bricks = [];
                                        wx.removeStorageSync('bricks');
                                    }

                                    wx.showToast({
                                        title: '本地清空成功',
                                        icon: 'success'
                                    });

                                    console.log('✅ 本地积木数据已清空');
                                } catch (localError) {
                                    console.error('❌ 本地清空失败:', localError);
                                    wx.showToast({
                                        title: '清空失败',
                                        icon: 'error'
                                    });
                                }
                            }
                        }
                    });
                });
            }
        }
    });
},