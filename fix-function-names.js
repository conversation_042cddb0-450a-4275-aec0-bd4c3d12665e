/**
 * 修复函数名称脚本
 * 将带有 "optimized" 前缀的函数名称修改为原始函数名称
 */

const fs = require('fs');
const path = require('path');

// 原始文件路径
const originalFilePath = path.join(__dirname, 'pages/bricks/bricks.js');
// 备份文件路径
const backupFilePath = path.join(__dirname, 'pages/bricks/bricks.js.bak2');

// 读取原始文件
console.log('读取原始文件...');
let content = fs.readFileSync(originalFilePath, 'utf8');

// 创建备份
console.log('创建备份文件...');
fs.writeFileSync(backupFilePath, content, 'utf8');

// 替换函数名称
console.log('开始替换函数名称...');

// 1. 替换 optimizedSetFilter 为 setFilter
console.log('替换 optimizedSetFilter 函数名称...');
content = content.replace(/function\s+optimizedSetFilter\(e\)/g, 'function setFilter(e)');

// 2. 替换 optimizedFilterBricks 为 filterBricks
console.log('替换 optimizedFilterBricks 函数名称...');
content = content.replace(/function\s+optimizedFilterBricks\(\)/g, 'function filterBricks()');

// 3. 替换 optimizedLoadBricksList 为 loadBricksList
console.log('替换 optimizedLoadBricksList 函数名称...');
content = content.replace(/async\s+function\s+optimizedLoadBricksList\(\)/g, 'async function loadBricksList()');

// 4. 替换 optimizedUpdateCounts 为 updateCounts
console.log('替换 optimizedUpdateCounts 函数名称...');
content = content.replace(/function\s+optimizedUpdateCounts\(\)/g, 'function updateCounts()');

// 写入修改后的文件
console.log('写入修改后的文件...');
fs.writeFileSync(originalFilePath, content, 'utf8');

console.log('函数名称修复完成！原始文件已备份为 bricks.js.bak2');