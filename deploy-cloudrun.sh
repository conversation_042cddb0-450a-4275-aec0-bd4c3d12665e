#!/bin/bash

# AI 简历云托管一键部署脚本
# 用于快速设置和部署微信云托管服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        return 1
    fi
    return 0
}

# 检查必需的工具
check_prerequisites() {
    log_info "检查必需的工具..."
    
    local missing_tools=()
    
    if ! check_command "node"; then
        missing_tools+=("Node.js")
    fi
    
    if ! check_command "npm"; then
        missing_tools+=("npm")
    fi
    
    if ! check_command "git"; then
        missing_tools+=("git")
    fi
    
    if ! check_command "curl"; then
        missing_tools+=("curl")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必需的工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具后重新运行此脚本"
        exit 1
    fi
    
    log_success "所有必需的工具已安装"
}

# 检查 GitHub CLI
check_github_cli() {
    if ! check_command "gh"; then
        log_warning "GitHub CLI 未安装"
        log_info "正在安装 GitHub CLI..."
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if check_command "brew"; then
                brew install gh
            else
                log_error "请先安装 Homebrew 或手动安装 GitHub CLI"
                exit 1
            fi
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
            sudo apt update
            sudo apt install gh
        else
            log_error "不支持的操作系统，请手动安装 GitHub CLI"
            exit 1
        fi
    fi
    
    # 检查是否已登录
    if ! gh auth status &> /dev/null; then
        log_info "请登录 GitHub..."
        gh auth login
    fi
    
    log_success "GitHub CLI 已就绪"
}

# 检查 CloudBase CLI
check_cloudbase_cli() {
    if ! check_command "tcb"; then
        log_info "正在安装 CloudBase CLI..."
        npm install -g @cloudbase/cli@latest
    fi
    
    log_success "CloudBase CLI 已就绪"
}

# 创建项目文件
create_project_files() {
    log_info "创建项目文件..."
    
    # 检查是否在正确的目录
    if [ ! -f "cloudbaserc.json" ]; then
        log_error "未找到 cloudbaserc.json 文件"
        log_info "请确保在正确的项目目录中运行此脚本"
        exit 1
    fi
    
    # 创建必要的目录
    mkdir -p .github/workflows
    
    log_success "项目结构已创建"
}

# 设置 GitHub Secrets
setup_github_secrets() {
    log_info "设置 GitHub Secrets..."
    
    # 检查是否提供了环境变量
    if [ -z "$CLOUDBASE_ENV_ID" ] || [ -z "$CLOUDBASE_SECRET_ID" ] || [ -z "$CLOUDBASE_SECRET_KEY" ]; then
        log_warning "未检测到环境变量，将交互式设置"
        
        echo -n "请输入云开发环境 ID: "
        read CLOUDBASE_ENV_ID
        
        echo -n "请输入腾讯云 API Secret ID: "
        read CLOUDBASE_SECRET_ID
        
        echo -n "请输入腾讯云 API Secret Key: "
        read -s CLOUDBASE_SECRET_KEY
        echo
    fi
    
    # 验证输入
    if [ -z "$CLOUDBASE_ENV_ID" ] || [ -z "$CLOUDBASE_SECRET_ID" ] || [ -z "$CLOUDBASE_SECRET_KEY" ]; then
        log_error "必需的参数不能为空"
        exit 1
    fi
    
    # 设置 GitHub Secrets
    log_info "正在设置 GitHub Secrets..."
    
    gh secret set CLOUDBASE_ENV_ID --body "$CLOUDBASE_ENV_ID"
    gh secret set CLOUDBASE_SECRET_ID --body "$CLOUDBASE_SECRET_ID"
    gh secret set CLOUDBASE_SECRET_KEY --body "$CLOUDBASE_SECRET_KEY"
    
    log_success "GitHub Secrets 设置完成"
}

# 验证 CloudBase 配置
verify_cloudbase_config() {
    log_info "验证 CloudBase 配置..."
    
    # 登录 CloudBase
    tcb login --apiKeyId "$CLOUDBASE_SECRET_ID" --apiKey "$CLOUDBASE_SECRET_KEY"
    
    # 验证环境
    if tcb env:list | grep -q "$CLOUDBASE_ENV_ID"; then
        log_success "CloudBase 环境验证成功"
    else
        log_error "CloudBase 环境验证失败"
        exit 1
    fi
}

# 部署到云托管
deploy_to_cloudrun() {
    log_info "部署到微信云托管..."
    
    # 构建 Docker 镜像
    log_info "构建 Docker 镜像..."
    docker build -t resume-snapshot:latest .
    
    # 测试镜像
    log_info "测试 Docker 镜像..."
    docker run -d --name test-container -p 8080:80 resume-snapshot:latest
    sleep 10
    
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_success "Docker 镜像测试通过"
    else
        log_error "Docker 镜像测试失败"
        docker stop test-container 2>/dev/null || true
        docker rm test-container 2>/dev/null || true
        exit 1
    fi
    
    docker stop test-container
    docker rm test-container
    
    # 部署到云托管
    log_info "正在部署到云托管..."
    tcb framework deploy --envId "$CLOUDBASE_ENV_ID"
    
    log_success "部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 这里可以添加具体的验证逻辑
    log_success "部署验证完成"
}

# 主函数
main() {
    echo "🚀 AI 简历云托管一键部署脚本"
    echo "=================================="
    
    # 检查先决条件
    check_prerequisites
    check_github_cli
    check_cloudbase_cli
    
    # 创建项目文件
    create_project_files
    
    # 设置 GitHub Secrets
    setup_github_secrets
    
    # 验证 CloudBase 配置
    verify_cloudbase_config
    
    # 部署到云托管
    deploy_to_cloudrun
    
    # 验证部署
    verify_deployment
    
    echo
    log_success "🎉 部署完成！"
    echo
    log_info "接下来的步骤："
    echo "1. 推送代码到 GitHub 触发自动部署"
    echo "2. 在 GitHub Actions 中查看部署状态"
    echo "3. 在微信云托管控制台查看服务状态"
    echo
    log_info "有用的链接："
    echo "- GitHub Actions: https://github.com/$(gh repo view --json owner,name -q '.owner.login + \"/\" + .name')/actions"
    echo "- 云托管控制台: https://console.cloud.tencent.com/tcb"
}

# 运行主函数
main "$@"
