#!/bin/bash

# 创建干净的项目副本脚本
# 如果缓存清理仍然无效，使用此脚本创建全新的项目副本

echo "🔄 创建干净的项目副本..."

# 设置目标目录
TARGET_DIR="../ai-resume-clean"
CURRENT_DIR=$(pwd)

echo "📁 当前目录: $CURRENT_DIR"
echo "📁 目标目录: $TARGET_DIR"

# 检查目标目录是否存在
if [ -d "$TARGET_DIR" ]; then
    echo "⚠️  目标目录已存在，是否覆盖？(y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm -rf "$TARGET_DIR"
        echo "🗑️  已删除现有目录"
    else
        echo "❌ 操作取消"
        exit 1
    fi
fi

# 创建目标目录
mkdir -p "$TARGET_DIR"

echo "📋 复制项目文件..."

# 复制核心文件和目录，排除缓存和问题文件
rsync -av \
    --exclude=".git" \
    --exclude="node_modules" \
    --exclude="miniprogram_npm" \
    --exclude=".DS_Store" \
    --exclude="*.log" \
    --exclude="*.cache" \
    --exclude="*.tmp" \
    --exclude=".wxcloud_cache" \
    --exclude=".tcb_cache" \
    --exclude=".wechat_devtools" \
    --exclude="deployment-temp*" \
    --exclude="test-example*" \
    --exclude="*.tar.gz" \
    --exclude="test-output" \
    --exclude="coverage" \
    --exclude=".venv" \
    --exclude=".serverless" \
    --exclude="*.backup.*" \
    --exclude="deep-wechat-cache-cleanup.sh" \
    --exclude="force-wechat-cache-cleanup.sh" \
    --exclude="create-clean-project-copy.sh" \
    . "$TARGET_DIR/"

echo "🔧 重新生成项目配置..."

# 进入目标目录
cd "$TARGET_DIR"

# 重新生成package-lock.json（如果存在package.json）
if [ -f "package.json" ]; then
    echo "📦 重新安装npm依赖..."
    npm install
fi

# 验证关键文件
echo "✅ 验证项目文件..."
if [ -f "app.json" ]; then
    echo "  ✅ app.json 存在"
else
    echo "  ❌ app.json 缺失"
fi

if [ -f "project.config.json" ]; then
    echo "  ✅ project.config.json 存在"
else
    echo "  ❌ project.config.json 缺失"
fi

if [ -d "pages" ]; then
    echo "  ✅ pages 目录存在"
else
    echo "  ❌ pages 目录缺失"
fi

if [ -d "cloudfunctions" ]; then
    echo "  ✅ cloudfunctions 目录存在"
else
    echo "  ❌ cloudfunctions 目录缺失"
fi

echo ""
echo "🎉 干净的项目副本创建完成！"
echo ""
echo "📋 下一步操作："
echo "1. 在微信开发者工具中关闭当前项目"
echo "2. 选择 '导入项目'"
echo "3. 选择新的项目目录: $TARGET_DIR"
echo "4. 等待项目编译完成"
echo "5. 重新进行真机调试"
echo ""
echo "🔧 如果新项目正常工作："
echo "1. 可以删除原项目目录"
echo "2. 将新项目重命名为原名称"
echo "3. 更新git远程仓库（如果需要）"
echo ""
echo "📁 项目路径信息："
echo "  原项目: $CURRENT_DIR"
echo "  新项目: $TARGET_DIR"
