#!/usr/bin/env python3
"""
删除包含敏感信息的文件
"""

import os
import shutil
import glob

def remove_sensitive_files():
    print("🗑️ 删除包含敏感信息的文件...")
    
    # 需要删除的文件和目录
    sensitive_paths = [
        '.taskmaster/',
        'utils/各种权限密码',
        'clean-secrets.sh',
        'scf-architecture/config/environment.yml',
        # 任何包含敏感信息的文件
    ]
    
    # 需要删除的文件模式
    sensitive_patterns = [
        '**/task_*.txt',
        '**/tasks_*.json',
        '**/*密码*',
        '**/*secret*',
        '**/*key*',
    ]
    
    removed_count = 0
    
    # 删除指定路径
    for path in sensitive_paths:
        if os.path.exists(path):
            if os.path.isdir(path):
                shutil.rmtree(path)
                print(f"✅ 删除目录: {path}")
            else:
                os.remove(path)
                print(f"✅ 删除文件: {path}")
            removed_count += 1
    
    # 删除匹配模式的文件
    for pattern in sensitive_patterns:
        for file_path in glob.glob(pattern, recursive=True):
            if os.path.exists(file_path) and os.path.isfile(file_path):
                try:
                    os.remove(file_path)
                    print(f"✅ 删除文件: {file_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"⚠️ 无法删除 {file_path}: {e}")
    
    print(f"🎉 删除完成！共删除 {removed_count} 个文件/目录")

if __name__ == "__main__":
    remove_sensitive_files()
