const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const puppeteer = require('puppeteer');

const app = express();
const PORT = process.env.PORT || 80;

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: false, // 允许内联样式，用于HTML渲染
}));

// 启用 CORS
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  credentials: true
}));

// 启用压缩
app.use(compression());

// 日志中间件
app.use(morgan('combined'));

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: process.env.RATE_LIMIT_MAX || 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康检查端点
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'resume-snapshot',
    version: '1.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: 'AI简历系统截图服务',
    service: 'resume-snapshot',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      snapshot: '/resume-snapshot',
      ping: '/ping'
    },
    documentation: 'https://github.com/xuyuzeamazon/ai-resume-cloudrun'
  });
});

// Ping 端点
app.get('/ping', (req, res) => {
  res.json({ 
    message: 'pong', 
    timestamp: new Date().toISOString() 
  });
});

// 简历截图服务主端点
app.post('/resume-snapshot', async (req, res) => {
  let browser = null;
  const startTime = Date.now();
  
  try {
    const { html, options = {} } = req.body;
    
    // 验证输入
    if (!html || typeof html !== 'string') {
      return res.status(400).json({ 
        error: '缺少有效的HTML内容',
        code: 'INVALID_HTML_CONTENT',
        timestamp: new Date().toISOString()
      });
    }

    console.log(`开始生成截图，HTML长度: ${html.length}`);

    // 启动浏览器
    browser = await puppeteer.launch({
      headless: 'new',
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    });

    const page = await browser.newPage();
    
    // 设置页面大小和选项
    const viewport = {
      width: options.width || 1200,
      height: options.height || 1600,
      deviceScaleFactor: options.deviceScaleFactor || 2
    };
    
    await page.setViewport(viewport);

    // 设置HTML内容
    await page.setContent(html, {
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    });

    // 等待字体加载
    await page.evaluateHandle('document.fonts.ready');

    // 生成截图
    const screenshotOptions = {
      type: options.format || 'png',
      fullPage: options.fullPage !== false,
      quality: options.format === 'jpeg' ? (options.quality || 90) : undefined,
      omitBackground: options.omitBackground || false
    };

    const screenshot = await page.screenshot(screenshotOptions);
    
    const processingTime = Date.now() - startTime;
    console.log(`截图生成成功，耗时: ${processingTime}ms`);

    // 设置响应头
    const format = options.format || 'png';
    res.setHeader('Content-Type', `image/${format}`);
    res.setHeader('Content-Disposition', `attachment; filename="resume-snapshot.${format}"`);
    res.setHeader('X-Processing-Time', `${processingTime}ms`);
    
    res.send(screenshot);

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('截图生成失败:', error);
    
    res.status(500).json({
      error: '截图生成失败',
      message: error.message,
      code: 'SCREENSHOT_GENERATION_FAILED',
      processingTime: `${processingTime}ms`,
      timestamp: new Date().toISOString()
    });
  } finally {
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('关闭浏览器失败:', closeError);
      }
    }
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    error: '内部服务器错误',
    message: process.env.NODE_ENV === 'development' ? error.message : '服务暂时不可用',
    code: 'INTERNAL_SERVER_ERROR',
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: '接口不存在',
    path: req.path,
    method: req.method,
    code: 'ENDPOINT_NOT_FOUND',
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 AI简历截图服务启动成功`);
  console.log(`📍 服务地址: http://0.0.0.0:${PORT}`);
  console.log(`🏥 健康检查: http://0.0.0.0:${PORT}/health`);
  console.log(`📸 截图服务: http://0.0.0.0:${PORT}/resume-snapshot`);
  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
});

// 优雅关闭
const gracefulShutdown = (signal) => {
  console.log(`收到${signal}信号，正在优雅关闭...`);
  server.close(() => {
    console.log('HTTP服务器已关闭');
    process.exit(0);
  });
  
  // 强制退出超时
  setTimeout(() => {
    console.error('强制退出');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
