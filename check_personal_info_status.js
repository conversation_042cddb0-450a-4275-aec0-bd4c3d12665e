/**
 * 检查个人信息模块当前状态
 * 在微信开发者工具Console中运行
 */

function checkPersonalInfoStatus() {
  console.log('🔍 检查个人信息模块当前状态...');
  
  try {
    // 1. 检查当前页面
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route !== 'pages/bricks/bricks') {
      console.log('⚠️ 请先导航到积木库页面 (pages/bricks/bricks)');
      return false;
    }
    
    // 2. 检查页面数据
    const bricks = currentPage.data.bricks || [];
    const personalCount = currentPage.data.personalCount || 0;
    
    console.log('📊 当前页面数据:');
    console.log('- 总积木数:', bricks.length);
    console.log('- 个人信息积木数:', personalCount);
    
    // 3. 查找个人信息积木
    const personalBricks = bricks.filter(brick => 
      brick.category === 'personal' || 
      brick.category === '个人' || 
      brick.category === '个人信息'
    );
    
    console.log('👤 个人信息积木详情:');
    if (personalBricks.length > 0) {
      personalBricks.forEach((brick, index) => {
        console.log(`积木 ${index + 1}:`, {
          id: brick.id,
          title: brick.title,
          description: brick.description,
          category: brick.category,
          source: brick.source
        });
        
        // 检查是否还有"暂无积木数据"问题
        if (brick.title === '暂无积木数据' || brick.description === '暂无积木数据') {
          console.log('❌ 仍然存在"暂无积木数据"问题');
          return false;
        }
      });
      
      console.log('✅ 个人信息模块显示正常');
      return true;
    } else {
      console.log('⚠️ 未找到个人信息积木');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
    return false;
  }
}

// 运行检查
console.log('🚀 开始检查个人信息模块状态...');
const isFixed = checkPersonalInfoStatus();

if (isFixed) {
  console.log('🎉 个人信息模块已修复！');
  console.log('💡 当前错误是AI+服务超时问题，与个人信息显示无关');
} else {
  console.log('🔧 个人信息模块仍需修复');
  console.log('💡 请运行修复脚本: oneClickFix()');
}
