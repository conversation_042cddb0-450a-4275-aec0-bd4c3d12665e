/**
 * 积木数据持久化功能测试脚本
 * 用于验证积木数据的保存和加载功能是否正常工作
 */

const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

const db = app.database();

/**
 * 测试积木数据保存功能
 */
async function testBrickSave() {
  console.log('🧪 测试积木数据保存功能...\n');
  
  try {
    // 创建测试积木数据
    const testBrick = {
      id: `test_brick_${Date.now()}`,
      title: '测试积木标题',
      content: '这是一个测试积木的内容',
      category: 'personal',
      tags: ['测试', '验证'],
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      usageCount: 0,
      isActive: true,
      _openid: 'test_user_openid'
    };

    // 调用云函数保存积木
    const result = await app.callFunction({
      name: 'brickManager',
      data: {
        action: 'add',
        data: testBrick
      }
    });

    console.log('📊 保存结果:', JSON.stringify(result.result, null, 2));
    
    if (result.result.success) {
      console.log('✅ 积木保存成功');
      return testBrick.id;
    } else {
      console.log('❌ 积木保存失败:', result.result.error);
      return null;
    }
  } catch (error) {
    console.error('❌ 测试积木保存时发生错误:', error);
    return null;
  }
}

/**
 * 测试积木数据加载功能
 */
async function testBrickLoad() {
  console.log('\n🧪 测试积木数据加载功能...\n');
  
  try {
    // 调用云函数加载积木
    const result = await app.callFunction({
      name: 'brickManager',
      data: {
        action: 'list',
        data: {
          limit: 10,
          sortBy: 'updateTime',
          sortOrder: 'desc'
        }
      }
    });

    console.log('📊 加载结果:', JSON.stringify(result.result, null, 2));
    
    if (result.result.success) {
      const bricks = result.result.data.bricks || [];
      console.log(`✅ 积木加载成功，共 ${bricks.length} 个积木`);
      
      if (bricks.length > 0) {
        console.log('📋 积木列表:');
        bricks.forEach((brick, index) => {
          console.log(`  ${index + 1}. ${brick.title} (${brick.category})`);
        });
      }
      
      return bricks;
    } else {
      console.log('❌ 积木加载失败:', result.result.error);
      return [];
    }
  } catch (error) {
    console.error('❌ 测试积木加载时发生错误:', error);
    return [];
  }
}

/**
 * 测试数据库直接查询
 */
async function testDirectDatabaseQuery() {
  console.log('\n🧪 测试数据库直接查询...\n');
  
  try {
    const result = await db.collection('bricks').limit(10).get();
    console.log(`📊 数据库直接查询结果: 共 ${result.data.length} 条记录`);
    
    if (result.data.length > 0) {
      console.log('📋 数据库中的积木:');
      result.data.forEach((brick, index) => {
        console.log(`  ${index + 1}. ${brick.title || '无标题'} (ID: ${brick.id || brick._id})`);
      });
    } else {
      console.log('⚠️ 数据库中没有积木数据');
    }
    
    return result.data;
  } catch (error) {
    console.error('❌ 数据库直接查询失败:', error);
    return [];
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData(testBrickId) {
  if (!testBrickId) return;
  
  console.log('\n🧹 清理测试数据...\n');
  
  try {
    const result = await app.callFunction({
      name: 'brickManager',
      data: {
        action: 'delete',
        data: {
          id: testBrickId
        }
      }
    });

    if (result.result.success) {
      console.log('✅ 测试数据清理成功');
    } else {
      console.log('⚠️ 测试数据清理失败:', result.result.error);
    }
  } catch (error) {
    console.error('❌ 清理测试数据时发生错误:', error);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始积木数据持久化功能测试\n');
  console.log('=' .repeat(50));
  
  try {
    // 1. 测试数据库直接查询（查看当前状态）
    await testDirectDatabaseQuery();
    
    // 2. 测试积木数据加载功能
    const existingBricks = await testBrickLoad();
    
    // 3. 测试积木数据保存功能
    const testBrickId = await testBrickSave();
    
    // 4. 再次测试加载功能（验证保存是否成功）
    if (testBrickId) {
      console.log('\n🔄 验证保存效果，重新加载积木数据...');
      await testBrickLoad();
      
      // 5. 清理测试数据
      await cleanupTestData(testBrickId);
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('✅ 积木数据持久化功能测试完成');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  runTests().then(() => {
    console.log('\n🎯 测试脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testBrickSave,
  testBrickLoad,
  testDirectDatabaseQuery
};
