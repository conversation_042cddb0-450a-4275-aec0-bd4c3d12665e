#!/bin/bash

# AI简历系统 - GitHub仓库初始化脚本
# 用于设置GitHub仓库和CI/CD流水线

set -e

echo "🚀 AI简历系统 - GitHub仓库初始化"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 1. 检查环境
print_message $BLUE "📋 1. 检查环境..."
if ! command -v git &> /dev/null; then
    print_message $RED "❌ Git未安装，请先安装Git"
    exit 1
fi

if ! command -v gh &> /dev/null; then
    print_message $YELLOW "⚠️ GitHub CLI未安装，将使用手动方式"
    USE_GH_CLI=false
else
    USE_GH_CLI=true
fi

print_message $GREEN "✅ 环境检查完成"

# 2. 项目信息
REPO_NAME="ai-resume"
REPO_DESCRIPTION="AI简历系统 - 微信云托管版本，支持简历解析、职位分析和预览截图功能"
GITHUB_USER="xuyuzeamazon"

print_message $BLUE "📋 2. 项目信息"
echo "仓库名称: $REPO_NAME"
echo "仓库描述: $REPO_DESCRIPTION"
echo "GitHub用户: $GITHUB_USER"
echo "项目目录: $(pwd)"

# 3. 初始化Git仓库
print_message $BLUE "📝 3. 初始化Git仓库..."

if [ ! -d ".git" ]; then
    git init
    print_message $GREEN "✅ Git仓库已初始化"
else
    print_message $YELLOW "⚠️ Git仓库已存在"
fi

# 4. 创建.gitignore文件
print_message $BLUE "📝 4. 创建.gitignore文件..."
cat > .gitignore << 'EOF'
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.production

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Test output
test-output/
test-*.png
test-*.jpg

# Deployment
deployment-temp/
*.tar.gz
*.zip

# CloudBase
.cloudbase/
EOF

print_message $GREEN "✅ .gitignore文件已创建"

# 5. 添加文件到Git
print_message $BLUE "📝 5. 添加文件到Git..."
git add .
git commit -m "Initial commit: AI Resume System with GitHub CI/CD pipeline

- 添加微信云托管截图服务
- 配置GitHub Actions自动部署
- 包含完整的CI/CD流水线
- 支持简历解析、职位分析和预览截图功能"

print_message $GREEN "✅ 初始提交完成"

# 6. 创建GitHub仓库
print_message $BLUE "📝 6. 创建GitHub仓库..."

if [ "$USE_GH_CLI" = true ]; then
    # 使用GitHub CLI创建仓库
    print_message $BLUE "使用GitHub CLI创建仓库..."
    gh repo create $REPO_NAME --public --description "$REPO_DESCRIPTION" --push
    print_message $GREEN "✅ GitHub仓库已创建并推送"
else
    # 手动创建仓库
    print_message $YELLOW "请手动创建GitHub仓库："
    echo "1. 访问: https://github.com/new"
    echo "2. 仓库名称: $REPO_NAME"
    echo "3. 描述: $REPO_DESCRIPTION"
    echo "4. 设置为公开仓库"
    echo "5. 不要初始化README、.gitignore或LICENSE"
    echo ""
    read -p "创建完成后按Enter继续..."
    
    # 添加远程仓库
    git remote add origin https://github.com/$GITHUB_USER/$REPO_NAME.git
    git branch -M main
    git push -u origin main
    print_message $GREEN "✅ 代码已推送到GitHub"
fi

# 7. 配置GitHub Secrets
print_message $BLUE "🔐 7. 配置GitHub Secrets..."
print_message $YELLOW "请在GitHub仓库中配置以下Secrets："
echo ""
echo "访问: https://github.com/$GITHUB_USER/$REPO_NAME/settings/secrets/actions"
echo ""
echo "需要添加的Secrets："
echo "1. CLOUDBASE_SECRET_ID - 腾讯云SecretId"
echo "2. CLOUDBASE_SECRET_KEY - 腾讯云SecretKey"
echo "3. CLOUDBASE_ENV_ID - zemuresume-4gjvx1wea78e3d1e"
echo ""
print_message $BLUE "获取腾讯云密钥："
echo "访问: https://console.cloud.tencent.com/cam/capi"
echo ""

read -p "配置完成后按Enter继续..."

# 8. 验证GitHub Actions
print_message $BLUE "🔍 8. 验证GitHub Actions..."
ACTIONS_URL="https://github.com/$GITHUB_USER/$REPO_NAME/actions"
print_message $BLUE "GitHub Actions页面: $ACTIONS_URL"

echo ""
print_message $YELLOW "验证步骤："
echo "1. 访问GitHub Actions页面"
echo "2. 查看是否有工作流运行"
echo "3. 检查部署状态"

# 9. 测试部署
print_message $BLUE "🧪 9. 测试部署..."
echo ""
print_message $YELLOW "触发部署的方法："
echo "1. 推送代码到main分支（自动触发）"
echo "2. 在GitHub Actions页面手动运行"
echo "3. 修改cloud-run/resume-snapshot/目录下的文件"

echo ""
print_message $BLUE "部署后验证："
echo "1. 健康检查: https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/health"
echo "2. 查看GitHub Actions日志"
echo "3. 测试截图服务功能"

# 10. 完成
print_message $GREEN "🎉 GitHub仓库初始化完成！"
echo ""
print_message $BLUE "📋 后续步骤："
echo "1. 确保GitHub Secrets已正确配置"
echo "2. 推送代码触发首次部署"
echo "3. 监控GitHub Actions执行情况"
echo "4. 验证微信云托管服务正常运行"

echo ""
print_message $BLUE "🔗 重要链接："
echo "- GitHub仓库: https://github.com/$GITHUB_USER/$REPO_NAME"
echo "- GitHub Actions: $ACTIONS_URL"
echo "- 云托管服务: https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com"
echo "- 腾讯云控制台: https://console.cloud.tencent.com/tcb"

echo ""
print_message $YELLOW "💡 提示："
echo "- 首次部署可能需要5-10分钟"
echo "- 如有问题，查看GitHub Actions日志"
echo "- 可以使用deploy-github.sh脚本进行本地测试"
