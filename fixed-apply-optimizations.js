/**
 * 积木页面性能优化应用脚本（修复版）
 * 用于将优化后的函数应用到原始文件中
 * 修复了与微信小程序环境的兼容性问题
 */

// 导入优化后的函数
const {
    optimizedSetFilter,
    optimizedFilterBricks,
    optimizedLoadBricksList,
    optimizedUpdateCounts,
    _processBricksData
} = require('./fixed-bricks-functions.js');

// 导入文件系统模块
const fs = require('fs');
const path = require('path');

// 原始文件路径
const originalFilePath = path.join(__dirname, 'pages/bricks/bricks.js');
// 备份文件路径
const backupFilePath = path.join(__dirname, 'pages/bricks/bricks.js.bak');

// 读取原始文件
console.log('读取原始文件...');
let content = fs.readFileSync(originalFilePath, 'utf8');

// 创建备份
console.log('创建备份文件...');
fs.writeFileSync(backupFilePath, content, 'utf8');

// 替换函数
console.log('开始替换函数...');

// 1. 替换 setFilter 函数
console.log('替换 setFilter 函数...');
const setFilterRegex = /setFilter\(e\)\s*\{[\s\S]*?this\.filterBricks\(\)[\s\S]*?this\.filterResumes\(\)[\s\S]*?\}/;
if (setFilterRegex.test(content)) {
    content = content.replace(setFilterRegex, optimizedSetFilter.toString().replace(/^function\s+optimizedSetFilter/, 'setFilter'));
    console.log('✅ setFilter 函数替换成功');
} else {
    console.log('⚠️ 未找到 setFilter 函数，跳过替换');
}

// 2. 替换 filterBricks 函数
console.log('替换 filterBricks 函数...');
const filterBricksRegex = /filterBricks\(\)\s*\{[\s\S]*?this\.setData\(\{\s*filteredBricks[\s\S]*?\}\)/;
if (filterBricksRegex.test(content)) {
    content = content.replace(filterBricksRegex, optimizedFilterBricks.toString().replace(/^function\s+optimizedFilterBricks/, 'filterBricks'));
    console.log('✅ filterBricks 函数替换成功');
} else {
    console.log('⚠️ 未找到 filterBricks 函数，跳过替换');
}

// 3. 替换 loadBricksList 函数
console.log('替换 loadBricksList 函数...');
const loadBricksListRegex = /loadBricksList\(\)\s*\{[\s\S]*?this\.setData\(\{[\s\S]*?loading[\s\S]*?\}\)[\s\S]*?\}/;
if (loadBricksListRegex.test(content)) {
    content = content.replace(loadBricksListRegex, optimizedLoadBricksList.toString().replace(/^function\s+optimizedLoadBricksList/, 'loadBricksList'));
    console.log('✅ loadBricksList 函数替换成功');
} else {
    console.log('⚠️ 未找到 loadBricksList 函数，跳过替换');
}

// 4. 替换 updateCounts 函数
console.log('替换 updateCounts 函数...');
const updateCountsRegex = /updateCounts\(\)\s*\{[\s\S]*?this\.setData\(\{[\s\S]*?resumeCount[\s\S]*?\}\)[\s\S]*?\}/;
if (updateCountsRegex.test(content)) {
    content = content.replace(updateCountsRegex, optimizedUpdateCounts.toString().replace(/^function\s+optimizedUpdateCounts/, 'updateCounts'));
    console.log('✅ updateCounts 函数替换成功');
} else {
    console.log('⚠️ 未找到 updateCounts 函数，跳过替换');
}

// 5. 添加 _processBricksData 辅助函数
console.log('添加 _processBricksData 辅助函数...');
const processBricksDataFunction = _processBricksData.toString();
// 在文件末尾添加辅助函数
const lastBracketIndex = content.lastIndexOf('}');
if (lastBracketIndex !== -1) {
    content = content.slice(0, lastBracketIndex) +
        '\n\n  // 辅助函数：处理积木数据\n  ' +
        processBricksDataFunction +
        '\n' +
        content.slice(lastBracketIndex);
    console.log('✅ _processBricksData 函数添加成功');
} else {
    console.log('⚠️ 无法找到合适位置添加 _processBricksData 函数');
}

// 添加缓存相关的初始化代码
console.log('添加缓存初始化代码...');
const onLoadRegex = /onLoad\(options\)\s*\{[\s\S]*?this\.initPage\(\);?\s*\}/;
const cacheInitCode = `
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化缓存对象
    this.filterCache = {};
    this.bricksDataCache = null;
    this.bricksDataCacheTime = 0;
    
    console.log('积木页面加载，初始化缓存机制');
    this.initPage();
  }`;

if (onLoadRegex.test(content)) {
    content = content.replace(onLoadRegex, cacheInitCode);
    console.log('✅ 缓存初始化代码添加成功');
} else {
    console.log('⚠️ 未找到 onLoad 函数，跳过添加缓存初始化代码');
}

// 写入修改后的文件
console.log('写入修改后的文件...');
fs.writeFileSync(originalFilePath, content, 'utf8');

console.log('优化完成！原始文件已备份为 bricks.js.bak');