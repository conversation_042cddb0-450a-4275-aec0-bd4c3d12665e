// 微信小程序简历预览图片生成功能测试脚本
// 在微信开发者工具的Console中运行此脚本

console.log('🖼️ 开始测试简历预览图片生成功能...');

// 测试配置
const TEST_CONFIG = {
  maxRetries: 3,
  timeoutMs: 35000, // 35秒超时
  expectedSuccessRate: 0.95, // 95%成功率
  maxResponseTime: 30000, // 30秒最大响应时间
  minImageSize: 50000, // 最小图片大小50KB
  maxImageSize: 5000000 // 最大图片大小5MB
};

// 测试结果统计
let testResults = {
  totalTests: 0,
  successCount: 0,
  failureCount: 0,
  responseTimes: [],
  errors: [],
  imageUrls: [],
  imageSizes: []
};

// 真实简历测试数据（基于用户提供的简历）
const REAL_RESUME_DATA = {
  personalInfo: {
    name: "徐瑜泽 (<PERSON>)",
    email: "<EMAIL>",
    phone: "139-2830-3116",
    location: "上海，中国",
    title: "Account Manager | 国际电商 & 客户成功"
  },
  summary: "拥有超过 5 年电商平台商家运营与增长策略经验的专家，专注于驱动全链路商业增长。在亚马逊期间，成功管理超过 8 万名跨境卖家，通过数据驱动的策略将名下卖家总 GMV 提升至 1 亿美元。具备从 0 到 1 搭建运营体系、制定行业策略及赋能商家实现数字化转型的综合能力，并成功主导 AI 解决方案落地，将客户高频问题解决率提升至 90%。",
  workExperience: [
    {
      company: "亚马逊 (Amazon)",
      position: "商家运营 (Account Manager)",
      location: "上海",
      startDate: "2022年5月",
      endDate: "2025年5月",
      description: "核心业务增长驱动：全面负责超过 8 万名跨境卖家的全生命周期管理，通过精细化运营和增长策略，将名下卖家总 GMV 在 2024 年提升至 1 亿美元，实现 25% 的同比增长。运营体系搭建与推广：从 0 到 1 搭建并推广三级客户管理体系，通过差异化运营策略及配置 10 余项专属权益，将团队响应速度提升 40%。"
    },
    {
      company: "小米 (Xiaomi)",
      position: "高级市场专员 (Senior Marketing Specialist)",
      location: "北京",
      startDate: "2020年5月",
      endDate: "2022年5月",
      description: "新媒体矩阵增长：负责双微矩阵内容营销，通过热点追踪与活动策划，实现微博粉丝从 50 万增长至 85 万（+70%），策划的#有品锦鲤#等话题阅读量超 2 亿。生态伙伴关系管理：管理超过 50 家小米生态链商家，通过整合营销活动拉动商家 GMV 增长 63%。"
    }
  ],
  education: [
    {
      school: "广东培正学院",
      degree: "经济学学士",
      major: "经济学",
      startDate: "2013年9月",
      endDate: "2017年6月"
    }
  ],
  skills: [
    "商家运营与增长", "客户全生命周期管理", "数据驱动分析", "AI解决方案设计", 
    "项目管理", "市场营销", "内容策划", "直播体系搭建", "SOP制定", "GMV提升"
  ],
  projects: [
    {
      name: "ACCS客户获取项目",
      description: "主导一项针对亚马逊支付服务（ACCS）的客户获取计划，成功转化并获取了超过 200 名新的企业级卖家。"
    },
    {
      name: "AI解决方案平台搭建",
      description: "主导开发了一个 AI 赋能的解决方案搜索平台，成功解决了 90% 的客户支持问询，并节省团队每周超过 60 小时的重复性工作。"
    }
  ]
};

// 检查云开发初始化状态
function checkCloudInit() {
  console.log('📡 检查云开发初始化状态...');
  
  if (!wx.cloud) {
    console.error('❌ wx.cloud 不可用，请检查基础库版本');
    return false;
  }
  
  console.log('✅ wx.cloud 可用');
  return true;
}

// 检查用户登录状态
async function checkLoginStatus() {
  console.log('👤 检查用户登录状态...');
  
  try {
    const token = wx.getStorageSync('token');
    const app = getApp();
    const isLoggedIn = !!token || app.globalData.isLoggedIn;
    
    console.log('登录状态:', {
      hasToken: !!token,
      globalIsLoggedIn: app.globalData.isLoggedIn,
      finalStatus: isLoggedIn
    });
    
    return isLoggedIn;
  } catch (error) {
    console.error('❌ 检查登录状态失败:', error);
    return false;
  }
}

// 单次图片生成测试
async function testSingleImageGeneration(testIndex) {
  console.log(`\n🧪 开始第 ${testIndex + 1} 次图片生成测试...`);
  
  const startTime = Date.now();
  
  try {
    // 调用云函数生成图片
    const result = await wx.cloud.callFunction({
      name: 'resumePreviewGenerator',
      data: {
        resumeData: REAL_RESUME_DATA,
        format: 'png'
      }
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.log(`⏱️ 响应时间: ${responseTime}ms`);
    console.log('📡 云函数调用结果:', result);
    
    // 解析结果
    if (result.result.statusCode === 200) {
      const responseData = JSON.parse(result.result.body);
      
      if (responseData.success && responseData.data && responseData.data.png) {
        const pngData = responseData.data.png;
        
        // 验证图片数据
        const imageUrl = pngData.imageUrl;
        const fileSize = pngData.fileSize;
        
        console.log(`✅ 图片生成成功!`);
        console.log(`📸 图片URL: ${imageUrl}`);
        console.log(`📏 文件大小: ${fileSize} bytes (${(fileSize / 1024).toFixed(2)} KB)`);
        
        // 验证图片大小是否合理
        if (fileSize < TEST_CONFIG.minImageSize) {
          throw new Error(`图片文件过小: ${fileSize} bytes < ${TEST_CONFIG.minImageSize} bytes`);
        }
        
        if (fileSize > TEST_CONFIG.maxImageSize) {
          throw new Error(`图片文件过大: ${fileSize} bytes > ${TEST_CONFIG.maxImageSize} bytes`);
        }
        
        // 记录成功结果
        testResults.successCount++;
        testResults.responseTimes.push(responseTime);
        testResults.imageUrls.push(imageUrl);
        testResults.imageSizes.push(fileSize);
        
        return {
          success: true,
          responseTime,
          imageUrl,
          fileSize,
          note: pngData.note || '正常生成'
        };
        
      } else {
        throw new Error(responseData.error || '图片生成失败：未返回有效数据');
      }
    } else {
      throw new Error(`云函数调用失败，状态码: ${result.result.statusCode}`);
    }
    
  } catch (error) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.error(`❌ 第 ${testIndex + 1} 次测试失败:`, error.message);
    
    // 记录失败结果
    testResults.failureCount++;
    testResults.errors.push({
      testIndex: testIndex + 1,
      error: error.message,
      responseTime
    });
    
    return {
      success: false,
      error: error.message,
      responseTime
    };
  }
}

// 批量测试
async function runBatchTests(testCount = 3) {
  console.log(`🚀 开始批量测试，共 ${testCount} 次...`);
  
  testResults.totalTests = testCount;
  
  for (let i = 0; i < testCount; i++) {
    const result = await testSingleImageGeneration(i);
    
    // 测试间隔，避免过于频繁的请求
    if (i < testCount - 1) {
      console.log('⏳ 等待 3 秒后进行下一次测试...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
}

// 分析测试结果
function analyzeTestResults() {
  console.log('\n📊 测试结果分析:');
  console.log('==========================================');
  
  const successRate = testResults.successCount / testResults.totalTests;
  const avgResponseTime = testResults.responseTimes.length > 0 
    ? testResults.responseTimes.reduce((a, b) => a + b, 0) / testResults.responseTimes.length 
    : 0;
  
  const avgImageSize = testResults.imageSizes.length > 0
    ? testResults.imageSizes.reduce((a, b) => a + b, 0) / testResults.imageSizes.length
    : 0;
  
  console.log(`📈 总测试次数: ${testResults.totalTests}`);
  console.log(`✅ 成功次数: ${testResults.successCount}`);
  console.log(`❌ 失败次数: ${testResults.failureCount}`);
  console.log(`📊 成功率: ${(successRate * 100).toFixed(2)}% (目标: ${TEST_CONFIG.expectedSuccessRate * 100}%)`);
  console.log(`⏱️ 平均响应时间: ${avgResponseTime.toFixed(0)}ms (目标: ≤${TEST_CONFIG.maxResponseTime}ms)`);
  console.log(`📏 平均图片大小: ${(avgImageSize / 1024).toFixed(2)} KB`);
  
  // 性能评估
  const performancePass = successRate >= TEST_CONFIG.expectedSuccessRate && avgResponseTime <= TEST_CONFIG.maxResponseTime;
  
  console.log('\n🎯 性能目标达成情况:');
  console.log(`成功率目标 (≥95%): ${successRate >= TEST_CONFIG.expectedSuccessRate ? '✅ 达成' : '❌ 未达成'}`);
  console.log(`响应时间目标 (≤30s): ${avgResponseTime <= TEST_CONFIG.maxResponseTime ? '✅ 达成' : '❌ 未达成'}`);
  console.log(`图片大小合理性: ${avgImageSize >= TEST_CONFIG.minImageSize && avgImageSize <= TEST_CONFIG.maxImageSize ? '✅ 合理' : '❌ 异常'}`);
  
  // 错误分析
  if (testResults.errors.length > 0) {
    console.log('\n🔍 错误详情分析:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. 测试 ${error.testIndex}: ${error.error} (响应时间: ${error.responseTime}ms)`);
    });
  }
  
  // 生成的图片URL
  if (testResults.imageUrls.length > 0) {
    console.log('\n🖼️ 生成的图片URL:');
    testResults.imageUrls.forEach((url, index) => {
      console.log(`${index + 1}. ${url}`);
    });
  }
  
  return {
    performancePass,
    successRate,
    avgResponseTime,
    avgImageSize,
    errors: testResults.errors
  };
}

// 主测试函数
async function runImageGenerationTest() {
  console.log('🎯 微信小程序简历预览图片生成功能测试');
  console.log('==========================================');
  
  // 前置检查
  if (!checkCloudInit()) {
    return;
  }
  
  const isLoggedIn = await checkLoginStatus();
  if (!isLoggedIn) {
    console.warn('⚠️ 用户未登录，这可能导致测试失败');
    console.log('💡 建议先进行微信登录或匿名登录');
  }
  
  // 执行批量测试
  await runBatchTests(3);
  
  // 分析结果
  const analysis = analyzeTestResults();
  
  // 最终结论
  console.log('\n🏁 测试结论:');
  if (analysis.performancePass) {
    console.log('🎉 图片生成功能测试通过！性能指标达标。');
  } else {
    console.log('⚠️ 图片生成功能存在性能问题，需要进一步优化。');
    
    if (analysis.successRate < TEST_CONFIG.expectedSuccessRate) {
      console.log('🔧 建议检查：云函数稳定性、微信云托管服务状态');
    }
    
    if (analysis.avgResponseTime > TEST_CONFIG.maxResponseTime) {
      console.log('🔧 建议检查：网络连接、云托管服务响应速度、图片生成算法优化');
    }
  }
  
  // 提供CLS日志查询建议
  if (analysis.errors.length > 0) {
    console.log('\n🔍 错误排查建议:');
    console.log('1. 查看云函数 resumePreviewGenerator 的执行日志');
    console.log('2. 检查微信云托管截图服务的调用状态');
    console.log('3. 验证云存储权限和网络连接');
    console.log('4. 使用腾讯云CLS日志进行详细错误分析');
  }
  
  return analysis;
}

// 执行测试
runImageGenerationTest().catch(error => {
  console.error('❌ 测试执行失败:', error);
});
