# AI简历系统 - 微信云托管截图服务

[![Deploy to WeChat CloudRun](https://github.com/xuyuzeamazon/ai-resume-cloudrun/actions/workflows/deploy.yml/badge.svg)](https://github.com/xuyuzeamazon/ai-resume-cloudrun/actions/workflows/deploy.yml)

## 项目简介

这是AI简历系统的微信云托管截图服务，提供高质量的网页截图功能，支持简历预览图片生成。

## 功能特性

- 🖼️ **高质量截图**: 使用Puppeteer生成高清网页截图
- 🚀 **云原生部署**: 基于微信云托管的容器化部署
- 🔄 **自动化CI/CD**: GitHub Actions自动部署流水线
- 📱 **移动端优化**: 支持多种设备尺寸的截图
- 🎯 **简历专用**: 针对简历预览场景优化

## 技术架构

```
GitHub Repository
    ↓ (git push)
GitHub Actions
    ↓ (自动构建)
WeChat CloudRun
    ↓ (容器运行)
Screenshot Service
```

## 快速开始

### 1. 环境要求

- Node.js 18+
- Docker (本地开发)
- 微信云托管环境

### 2. 本地开发

```bash
# 克隆项目
git clone https://github.com/xuyuzeamazon/ai-resume-cloudrun.git
cd ai-resume-cloudrun

# 安装依赖
npm install

# 启动服务
npm start

# 健康检查
curl http://localhost:8080/health
```

### 3. API使用

#### 健康检查
```bash
GET /health
```

#### 生成截图
```bash
POST /snapshot
Content-Type: application/json

{
  "url": "https://example.com",
  "format": "png",
  "width": 800,
  "height": 600,
  "fullPage": true
}
```

## 部署配置

### GitHub Secrets

在GitHub仓库的Settings > Secrets中配置：

- `CLOUDBASE_SECRET_ID`: 腾讯云SecretId
- `CLOUDBASE_SECRET_KEY`: 腾讯云SecretKey  
- `CLOUDBASE_ENV_ID`: 云开发环境ID

### 自动部署

推送代码到main分支即可触发自动部署：

```bash
git add .
git commit -m "feat: 新功能"
git push origin main
```

## 服务监控

- **健康检查**: https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/health
- **GitHub Actions**: https://github.com/xuyuzeamazon/ai-resume-cloudrun/actions

## 开发指南

### 项目结构

```
├── index.js              # 主服务文件
├── package.json          # 项目配置
├── Dockerfile            # 容器配置
├── cloudbaserc.json      # 云开发配置
├── .github/
│   └── workflows/
│       └── deploy.yml    # CI/CD配置
└── README.md             # 项目文档
```

### 本地测试

```bash
# 启动服务
npm start

# 测试截图API
curl -X POST http://localhost:8080/snapshot \
  -H "Content-Type: application/json" \
  -d '{"url":"https://example.com","format":"png","width":800,"height":600}' \
  --output test.png
```

## 故障排除

### 常见问题

1. **部署失败**: 检查GitHub Secrets配置
2. **截图失败**: 检查目标URL是否可访问
3. **服务启动慢**: 容器冷启动需要时间

### 日志查看

- GitHub Actions日志: 仓库Actions页面
- 云托管日志: 微信云托管控制台

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License

## 联系我们

- 项目地址: https://github.com/xuyuzeamazon/ai-resume-cloudrun
- 问题反馈: GitHub Issues
