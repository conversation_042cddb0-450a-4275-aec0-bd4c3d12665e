#!/bin/bash

# 微信云托管配置验证脚本
# 用于验证修复后的配置是否正确

echo "🔍 微信云托管配置验证"
echo "=========================="

# 检查项目结构
echo "📁 检查项目结构..."

# 1. 检查根目录cloudbaserc.json
if [ -f "cloudbaserc.json" ]; then
    echo "✅ 根目录cloudbaserc.json存在"
    
    # 检查container配置
    if grep -q "resume-snapshot" cloudbaserc.json && grep -q "./cloud-run/resume-snapshot" cloudbaserc.json; then
        echo "✅ container配置指向正确的服务目录"
    else
        echo "❌ container配置可能有问题"
        echo "   请检查serviceName和servicePath配置"
    fi
else
    echo "❌ 根目录cloudbaserc.json不存在"
fi

# 2. 检查云托管服务目录
if [ -d "cloud-run/resume-snapshot" ]; then
    echo "✅ 云托管服务目录存在"
    
    cd cloud-run/resume-snapshot
    
    # 检查关键文件
    if [ -f "Dockerfile" ]; then
        echo "✅ Dockerfile存在"
        
        # 检查Dockerfile内容
        if grep -q "npm ci --omit=dev" Dockerfile; then
            echo "✅ Dockerfile使用了正确的npm安装命令"
        elif grep -q "npm install --only=production" Dockerfile; then
            echo "⚠️  Dockerfile使用了旧的npm命令，建议更新为 npm ci --omit=dev"
        fi
    else
        echo "❌ Dockerfile不存在"
    fi
    
    if [ -f "package.json" ]; then
        echo "✅ package.json存在"
        
        # 检查关键依赖
        if grep -q "puppeteer" package.json; then
            echo "✅ 包含puppeteer依赖"
        fi
        
        if grep -q "express" package.json; then
            echo "✅ 包含express依赖"
        fi
    else
        echo "❌ package.json不存在"
    fi
    
    if [ -f "index.js" ]; then
        echo "✅ index.js存在"
    else
        echo "❌ index.js不存在"
    fi
    
    cd ../..
else
    echo "❌ 云托管服务目录不存在"
fi

# 3. 检查GitHub Actions配置
if [ -f ".github/workflows/deploy.yml" ]; then
    echo "✅ GitHub Actions配置存在"
    
    # 检查关键配置
    if grep -q "cloud-run/resume-snapshot" .github/workflows/deploy.yml; then
        echo "✅ GitHub Actions包含云托管服务目录配置"
    fi
    
    if grep -q "CLOUDBASE_SECRET_ID" .github/workflows/deploy.yml; then
        echo "✅ GitHub Actions包含必要的环境变量"
    fi
else
    echo "❌ GitHub Actions配置不存在"
fi

echo ""
echo "🎯 配置验证总结"
echo "=================="

# 检查是否所有关键文件都存在
ERRORS=0

if [ ! -f "cloudbaserc.json" ]; then
    echo "❌ 缺少根目录cloudbaserc.json"
    ERRORS=$((ERRORS + 1))
fi

if [ ! -f "cloud-run/resume-snapshot/Dockerfile" ]; then
    echo "❌ 缺少云托管服务Dockerfile"
    ERRORS=$((ERRORS + 1))
fi

if [ ! -f "cloud-run/resume-snapshot/package.json" ]; then
    echo "❌ 缺少云托管服务package.json"
    ERRORS=$((ERRORS + 1))
fi

if [ ! -f "cloud-run/resume-snapshot/index.js" ]; then
    echo "❌ 缺少云托管服务index.js"
    ERRORS=$((ERRORS + 1))
fi

if [ ! -f ".github/workflows/deploy.yml" ]; then
    echo "❌ 缺少GitHub Actions配置"
    ERRORS=$((ERRORS + 1))
fi

if [ $ERRORS -eq 0 ]; then
    echo "✅ 所有配置文件检查通过！"
    echo ""
    echo "🚀 下一步操作："
    echo "1. 提交代码到GitHub仓库"
    echo "2. 推送到main分支触发自动部署"
    echo "3. 在GitHub Actions中查看部署日志"
    echo "4. 在微信云托管控制台验证服务状态"
else
    echo "❌ 发现 $ERRORS 个配置问题，请修复后重试"
fi

echo ""
echo "📋 部署命令参考："
echo "git add ."
echo "git commit -m 'fix: 修复微信云托管流水线构建配置'"
echo "git push origin main"
