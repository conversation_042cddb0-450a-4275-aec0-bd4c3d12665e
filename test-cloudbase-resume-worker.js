/**
 * 测试CloudBase resumeWorker云函数调用
 * 模拟微信小程序环境中的调用
 */

const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

async function testResumeWorkerCloudFunction() {
  try {
    console.log('🧪 测试CloudBase resumeWorker云函数调用...');
    
    // 模拟前端调用的参数
    const testData = {
      fileId: 'cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1341667342/resumes/test-resume.pdf',
      fileName: 'test-resume.pdf',
      fileType: 'pdf',
      userId: 'test-user-123'
      // 注意：没有传递taskId，应该走同步返回流程
    };
    
    console.log('📋 调用参数:', testData);
    
    const result = await app.callFunction({
      name: 'resumeWorker',
      data: testData
    });
    
    console.log('✅ 云函数调用完成');
    console.log('🔍 原始返回结果结构:', {
      hasResult: !!result.result,
      resultType: typeof result.result,
      resultKeys: result.result ? Object.keys(result.result) : [],
      hasStatusCode: result.result ? !!result.result.statusCode : false,
      hasBody: result.result ? !!result.result.body : false,
      hasSuccess: result.result ? !!result.result.success : false,
      hasData: result.result ? !!result.result.data : false,
      hasError: result.result ? !!result.result.error : false
    });
    
    // 模拟前端的处理逻辑
    let responseData = null;
    
    if (result.result) {
      // 情况1: HTTP响应格式 (包含statusCode, headers, body)
      if (result.result.statusCode && result.result.body) {
        console.log('📦 检测到HTTP响应格式，解析body内容');
        try {
          const bodyData = typeof result.result.body === 'string' 
            ? JSON.parse(result.result.body) 
            : result.result.body;
          
          console.log('📊 解析后的body数据:', bodyData);
          responseData = bodyData;
        } catch (parseError) {
          console.error('❌ 解析body失败:', parseError);
          throw new Error('云函数返回的body格式无效');
        }
      }
      // 情况2: 直接的JSON格式 (包含success, data, error)
      else if (result.result.hasOwnProperty('success')) {
        console.log('📦 检测到直接JSON格式');
        responseData = result.result;
      }
      // 情况3: 其他格式
      else {
        console.log('📦 检测到其他格式，尝试直接使用');
        responseData = result.result;
      }
    } else {
      throw new Error('云函数返回结果为空');
    }
    
    console.log('🔍 最终处理的响应数据:', {
      hasSuccess: !!responseData.success,
      hasData: !!responseData.data,
      hasError: !!responseData.error,
      successValue: responseData.success,
      dataType: typeof responseData.data,
      errorMessage: responseData.error
    });
    
    // 验证响应数据
    if (responseData && responseData.success) {
      console.log('✅ 云函数执行成功');
      console.log('📊 返回数据统计:');
      
      if (responseData.data) {
        const data = responseData.data;
        console.log('- 个人信息:', data.personalInfo ? '✅' : '❌');
        console.log('- 工作经历:', data.workExperience ? data.workExperience.length + '个' : '❌');
        console.log('- 教育背景:', data.education ? data.education.length + '个' : '❌');
        console.log('- 技能:', data.skills ? data.skills.length + '个' : '❌');
        console.log('- 项目:', data.projects ? data.projects.length + '个' : '❌');
        console.log('- 积木:', data.bricks ? data.bricks.length + '个' : '❌');
        console.log('- 处理时间:', responseData.processingTime + 'ms');
        console.log('- AI模型:', responseData.model || '未知');
      }
      
      return responseData;
    } else if (responseData && responseData.error) {
      console.error('❌ 云函数执行失败:', responseData.error);
      throw new Error(responseData.error);
    } else {
      console.error('❌ 云函数返回格式异常:', responseData);
      throw new Error('云函数返回格式异常: ' + JSON.stringify(responseData));
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('❌ 错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    throw error;
  }
}

// 运行测试
testResumeWorkerCloudFunction().then((result) => {
  console.log('🎉 测试成功完成！');
  console.log('📋 最终结果:', {
    success: result.success,
    hasData: !!result.data,
    processingTime: result.processingTime,
    model: result.model
  });
  process.exit(0);
}).catch(error => {
  console.error('💥 测试失败:', error.message);
  process.exit(1);
});
