/**
 * 统一身份验证系统测试脚本
 * 在微信开发者工具控制台中运行
 */

// 测试统一身份验证系统
async function testUnifiedAuth() {
  console.log('🧪 开始测试统一身份验证系统...');
  
  const results = {
    jwtAuth: false,
    cloudAuth: false,
    brickManagerAuth: false,
    bricksLoaded: false,
    error: null
  };

  try {
    const app = getApp();
    
    // 测试1: JWT身份验证
    console.log('\n🔍 测试1: JWT身份验证状态');
    const jwtValid = app.brickManager.checkJWTLoginStatus();
    results.jwtAuth = jwtValid;
    console.log('JWT身份验证结果:', jwtValid);

    // 测试2: 云开发身份验证
    console.log('\n🔍 测试2: 云开发身份验证');
    const cloudValid = await app.brickManager.verifyCloudAuth();
    results.cloudAuth = cloudValid;
    console.log('云开发身份验证结果:', cloudValid);

    // 测试3: 统一身份验证检查
    console.log('\n🔍 测试3: 统一身份验证检查');
    const unifiedValid = await app.brickManager.checkUserLoginStatus();
    results.brickManagerAuth = unifiedValid;
    console.log('统一身份验证结果:', unifiedValid);

    // 测试4: 积木数据加载
    if (unifiedValid) {
      console.log('\n🔍 测试4: 积木数据加载');
      const startTime = Date.now();
      const bricks = await app.brickManager.loadFromCloudDatabase();
      const endTime = Date.now();
      
      results.bricksLoaded = Array.isArray(bricks);
      console.log('积木加载结果:', {
        success: results.bricksLoaded,
        count: bricks.length,
        duration: endTime - startTime + 'ms'
      });
    }

    // 测试5: 完整初始化流程
    console.log('\n🔍 测试5: 完整初始化流程');
    const initStartTime = Date.now();
    const initBricks = await app.brickManager.init();
    const initEndTime = Date.now();
    
    console.log('初始化结果:', {
      success: Array.isArray(initBricks),
      count: initBricks.length,
      duration: initEndTime - initStartTime + 'ms'
    });

    console.log('\n✅ 统一身份验证测试完成');
    console.log('📊 测试结果汇总:', results);
    
    return results;

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    results.error = error.message;
    return results;
  }
}

// 测试云函数直接调用
async function testDirectCloudFunction() {
  console.log('🧪 测试云函数直接调用...');
  
  const results = {
    ping: { success: false, openid: null, error: null },
    brickManager: { success: false, error: null }
  };

  try {
    // 测试ping云函数
    console.log('📡 测试ping云函数...');
    const pingResult = await wx.cloud.callFunction({
      name: 'ping',
      data: { action: 'test-auth' }
    });
    
    results.ping.success = pingResult.result?.success || false;
    results.ping.openid = pingResult.result?.openid || null;
    console.log('Ping结果:', pingResult.result);
    
    // 测试brickManager云函数
    console.log('📡 测试brickManager云函数...');
    const brickResult = await wx.cloud.callFunction({
      name: 'brickManager',
      data: {
        action: 'list',
        data: { limit: 1 }
      }
    });
    
    results.brickManager.success = brickResult.result?.success || false;
    console.log('BrickManager结果:', brickResult.result);
    
  } catch (error) {
    console.error('❌ 云函数测试失败:', error);
    if (error.message.includes('身份验证失败')) {
      results.brickManager.error = '身份验证失败';
    } else {
      results.brickManager.error = error.message;
    }
  }

  console.log('📊 云函数测试结果:', results);
  return results;
}

// 诊断身份验证状态
function diagnoseAuthState() {
  console.log('🔍 诊断当前身份验证状态...');
  
  const app = getApp();
  const diagnosis = {
    globalData: {
      isLoggedIn: app.globalData?.isLoggedIn,
      userInfo: !!app.globalData?.userInfo,
      sessionToken: !!app.globalData?.sessionToken,
      cloudOpenid: app.globalData?.cloudOpenid,
      isCloudConnected: app.globalData?.isCloudConnected
    },
    localStorage: {
      isLoggedIn: wx.getStorageSync('isLoggedIn'),
      userInfo: !!wx.getStorageSync('userInfo'),
      sessionToken: !!wx.getStorageSync('session_token'),
      userId: wx.getStorageSync('user_id'),
      openid: wx.getStorageSync('openid'),
      cloudOpenid: wx.getStorageSync('cloud_openid')
    }
  };

  console.log('📋 身份验证状态诊断:', diagnosis);
  return diagnosis;
}

// 运行完整测试套件
async function runFullTest() {
  console.log('🚀 开始运行完整测试套件...');
  
  // 1. 诊断当前状态
  const diagnosis = diagnoseAuthState();
  
  // 2. 测试云函数直接调用
  const cloudFunctionResults = await testDirectCloudFunction();
  
  // 3. 测试统一身份验证
  const unifiedAuthResults = await testUnifiedAuth();
  
  // 4. 生成测试报告
  const report = {
    timestamp: new Date().toISOString(),
    diagnosis: diagnosis,
    cloudFunctionTest: cloudFunctionResults,
    unifiedAuthTest: unifiedAuthResults,
    summary: {
      jwtWorking: unifiedAuthResults.jwtAuth,
      cloudWorking: unifiedAuthResults.cloudAuth,
      unifiedWorking: unifiedAuthResults.brickManagerAuth,
      bricksWorking: unifiedAuthResults.bricksLoaded,
      overallSuccess: unifiedAuthResults.brickManagerAuth && unifiedAuthResults.bricksLoaded
    }
  };

  console.log('\n📊 完整测试报告:', report);
  
  if (report.summary.overallSuccess) {
    console.log('✅ 统一身份验证系统修复成功！');
  } else {
    console.log('❌ 统一身份验证系统仍有问题，需要进一步修复');
  }
  
  return report;
}

// 导出测试函数到全局
global.testUnifiedAuth = testUnifiedAuth;
global.testDirectCloudFunction = testDirectCloudFunction;
global.diagnoseAuthState = diagnoseAuthState;
global.runFullTest = runFullTest;

console.log('🧪 统一身份验证测试脚本已加载');
console.log('请运行: runFullTest()');
