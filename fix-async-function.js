/**
 * 修复异步函数语法脚本
 * 将 async function 语法修改为兼容微信小程序的 Promise 语法
 */

const fs = require('fs');
const path = require('path');

// 原始文件路径
const originalFilePath = path.join(__dirname, 'pages/bricks/bricks.js');
// 备份文件路径
const backupFilePath = path.join(__dirname, 'pages/bricks/bricks.js.bak3');

// 读取原始文件
console.log('读取原始文件...');
let content = fs.readFileSync(originalFilePath, 'utf8');

// 创建备份
console.log('创建备份文件...');
fs.writeFileSync(backupFilePath, content, 'utf8');

// 替换异步函数语法
console.log('开始修复异步函数语法...');

// 替换 async function loadBricksList() 为 loadBricksList() 并修改内部实现
const asyncFunctionRegex = /async\s+function\s+loadBricksList\(\)\s*\{[\s\S]*?this\.setData\(\{\s*loading:\s*false[\s\S]*?\}\)[\s\S]*?\}/;
const newFunction = `loadBricksList() {
    console.log('开始加载积木列表')
    this.setData({ loading: true, loadingText: '加载积木中...' })
    
    // 检查缓存是否有效
    const now = Date.now();
    if (this.bricksDataCache && (now - this.bricksDataCacheTime < 60000)) {
      console.log('使用缓存的积木数据')
      // 使用缓存数据
      this._processBricksData(this.bricksDataCache);
      this.setData({ loading: false });
      return Promise.resolve();
    }
    
    // 从云端获取数据
    const BrickManager = require('../../utils/brick-manager');
    return BrickManager.getBricksList()
      .then(bricksData => {
        console.log('获取积木数据成功', bricksData.length)
        
        // 更新缓存
        this.bricksDataCache = bricksData;
        this.bricksDataCacheTime = now;
        
        // 处理数据
        this._processBricksData(bricksData);
      })
      .catch(err => {
        console.error('获取积木数据失败', err)
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      })
      .finally(() => {
        this.setData({ loading: false })
      });
  }`;

content = content.replace(asyncFunctionRegex, newFunction);

// 写入修改后的文件
console.log('写入修改后的文件...');
fs.writeFileSync(originalFilePath, content, 'utf8');

console.log('异步函数语法修复完成！原始文件已备份为 bricks.js.bak3');