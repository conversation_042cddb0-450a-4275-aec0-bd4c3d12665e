/**
 * 云开发AI+服务状态检查工具
 * 在微信开发者工具Console中运行，检查AI+服务配置和状态
 */

async function checkAIServiceStatus() {
  console.log('🔍 开始检查云开发AI+服务状态...');
  
  try {
    // 1. 检查云开发环境初始化
    console.log('📋 检查云开发环境...');
    if (typeof wx === 'undefined' || !wx.cloud) {
      console.log('❌ 微信云开发环境不可用');
      return false;
    }
    
    console.log('✅ 微信云开发环境正常');
    
    // 2. 测试云函数调用
    console.log('📡 测试云函数调用...');
    
    try {
      const testResult = await wx.cloud.callFunction({
        name: 'resumeWorker',
        data: {
          action: 'test',
          testContent: '这是一个测试内容'
        }
      });
      
      console.log('✅ 云函数调用成功:', testResult);
    } catch (error) {
      console.log('❌ 云函数调用失败:', error.message);
      
      if (error.message.includes('SERVICE_TIMEOUT')) {
        console.log('🚨 检测到AI+服务超时问题');
        return false;
      }
    }
    
    // 3. 检查AI+服务配置
    console.log('🤖 检查AI+服务配置...');
    
    // 尝试直接调用AI+服务
    try {
      const aiTestResult = await wx.cloud.callFunction({
        name: 'resumeWorker',
        data: {
          action: 'ai_test',
          prompt: '请回复"AI服务正常"'
        }
      });
      
      if (aiTestResult.result && aiTestResult.result.success) {
        console.log('✅ AI+服务正常工作');
        return true;
      } else {
        console.log('❌ AI+服务响应异常:', aiTestResult);
        return false;
      }
    } catch (error) {
      console.log('❌ AI+服务测试失败:', error.message);
      
      // 分析具体错误类型
      if (error.message.includes('SERVICE_TIMEOUT')) {
        console.log('🚨 AI+服务超时 - 可能原因:');
        console.log('1. AI+功能未在控制台启用');
        console.log('2. DeepSeek模型配置有误');
        console.log('3. API密钥未正确设置');
        console.log('4. 服务配额不足');
        console.log('5. 网络连接问题');
      } else if (error.message.includes('AI功能不可用')) {
        console.log('🚨 AI+功能未启用 - 请检查:');
        console.log('1. 访问控制台: https://tcb.cloud.tencent.com/dev#/ai');
        console.log('2. 启用AI+功能');
        console.log('3. 配置DeepSeek模型');
      }
      
      return false;
    }
    
  } catch (error) {
    console.error('❌ 检查过程失败:', error);
    return false;
  }
}

// 修复AI+服务超时问题
async function fixAIServiceTimeout() {
  console.log('🔧 开始修复AI+服务超时问题...');
  
  try {
    // 1. 重新部署resumeWorker云函数
    console.log('📦 重新部署resumeWorker云函数...');
    
    // 这里需要用户手动操作，提供指导
    console.log('⚠️ 需要手动操作:');
    console.log('1. 在微信开发者工具中右键点击 cloudfunctions/resumeWorker');
    console.log('2. 选择"上传并部署：云端安装依赖"');
    console.log('3. 等待部署完成');
    
    // 2. 检查云函数权限
    console.log('🔐 检查云函数权限...');
    console.log('请确保云函数具有以下权限:');
    console.log('- AI+服务调用权限');
    console.log('- 云数据库读写权限');
    console.log('- 云存储访问权限');
    
    // 3. 验证修复效果
    console.log('🧪 验证修复效果...');
    const isFixed = await checkAIServiceStatus();
    
    if (isFixed) {
      console.log('🎉 AI+服务修复成功！');
      return true;
    } else {
      console.log('❌ AI+服务仍有问题，请检查控制台配置');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 修复过程失败:', error);
    return false;
  }
}

// 提供控制台链接
function openAIConsole() {
  console.log('🌐 云开发AI+控制台链接:');
  console.log('https://tcb.cloud.tencent.com/dev?envId=zemuresume-4gjvx1wea78e3d1e#/ai');
  console.log('');
  console.log('📋 检查清单:');
  console.log('1. ✅ AI+功能是否已启用');
  console.log('2. ✅ DeepSeek模型是否已配置');
  console.log('3. ✅ API密钥是否正确设置');
  console.log('4. ✅ 服务配额是否充足');
  console.log('5. ✅ 计费是否正常');
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    checkAIServiceStatus,
    fixAIServiceTimeout,
    openAIConsole
  };
}

console.log('🔧 AI+服务状态检查工具已加载');
console.log('💡 使用方法:');
console.log('1. 运行 checkAIServiceStatus() 检查服务状态');
console.log('2. 运行 fixAIServiceTimeout() 修复超时问题');
console.log('3. 运行 openAIConsole() 获取控制台链接');
console.log('');
console.log('🚀 推荐先运行: checkAIServiceStatus()');
