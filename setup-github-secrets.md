# GitHub Secrets 设置指南

## 📋 必需的 Secrets

在 GitHub 仓库中设置以下 Secrets 以启用自动部署：

### 1. CLOUDBASE_ENV_ID
**描述**: 云开发环境 ID  
**获取方式**:
1. 登录 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 进入 [云开发控制台](https://console.cloud.tencent.com/tcb)
3. 选择或创建环境
4. 在环境概览页面复制环境 ID

**示例**: `zemuresume-4gjvx1wea78e3d1e`

### 2. CLOUDBASE_SECRET_ID
**描述**: 腾讯云 API 密钥 ID  
**获取方式**:
1. 登录 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 进入 [访问管理 > API密钥管理](https://console.cloud.tencent.com/cam/capi)
3. 点击"新建密钥"或使用现有密钥
4. 复制 SecretId

**示例**: `AKIDxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### 3. CLOUDBASE_SECRET_KEY
**描述**: 腾讯云 API 密钥 Key  
**获取方式**:
1. 在 API 密钥管理页面
2. 复制对应的 SecretKey（注意保密）

**示例**: `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

## 🔧 设置步骤

### 方法一：通过 GitHub 网页界面

1. 打开你的 GitHub 仓库
2. 点击 **Settings** 标签
3. 在左侧菜单中选择 **Secrets and variables** > **Actions**
4. 点击 **New repository secret**
5. 分别添加上述三个 secrets

### 方法二：使用 GitHub CLI

```bash
# 安装 GitHub CLI
# macOS: brew install gh
# Windows: winget install GitHub.cli
# Linux: 参考 https://github.com/cli/cli#installation

# 登录 GitHub
gh auth login

# 设置 secrets
gh secret set CLOUDBASE_ENV_ID --body "your-env-id"
gh secret set CLOUDBASE_SECRET_ID --body "your-secret-id"  
gh secret set CLOUDBASE_SECRET_KEY --body "your-secret-key"

# 验证设置
gh secret list
```

### 方法三：批量设置脚本

创建 `.env.secrets` 文件（不要提交到 Git）：

```bash
CLOUDBASE_ENV_ID=your-env-id
CLOUDBASE_SECRET_ID=your-secret-id
CLOUDBASE_SECRET_KEY=your-secret-key
```

运行设置脚本：

```bash
#!/bin/bash
# setup-secrets.sh

if [ ! -f ".env.secrets" ]; then
    echo "❌ .env.secrets 文件不存在"
    echo "请创建 .env.secrets 文件并填入相应的值"
    exit 1
fi

source .env.secrets

echo "🔐 设置 GitHub Secrets..."

gh secret set CLOUDBASE_ENV_ID --body "$CLOUDBASE_ENV_ID"
gh secret set CLOUDBASE_SECRET_ID --body "$CLOUDBASE_SECRET_ID"
gh secret set CLOUDBASE_SECRET_KEY --body "$CLOUDBASE_SECRET_KEY"

echo "✅ GitHub Secrets 设置完成！"
echo "🚀 现在可以推送代码触发自动部署了"

# 清理敏感文件
rm -f .env.secrets
echo "🧹 已清理临时文件"
```

## 🔒 安全注意事项

1. **不要在代码中硬编码密钥**
2. **定期轮换 API 密钥**
3. **使用最小权限原则**
4. **监控 API 密钥使用情况**

## 🛡️ 权限配置

确保 API 密钥具有以下权限：

- **云开发 (TCB)**: 完整权限
- **云托管**: 部署权限
- **容器镜像服务**: 推送/拉取权限

## ✅ 验证设置

设置完成后，可以通过以下方式验证：

1. **查看 Secrets 列表**:
   ```bash
   gh secret list
   ```

2. **触发测试部署**:
   ```bash
   git commit --allow-empty -m "test: 触发部署测试"
   git push origin main
   ```

3. **查看 Actions 日志**:
   - 进入 GitHub 仓库
   - 点击 **Actions** 标签
   - 查看最新的工作流运行状态

## 🐛 常见问题

### Q: 提示权限不足
**A**: 检查 API 密钥是否具有云开发和云托管的相关权限

### Q: 环境 ID 找不到
**A**: 确保环境 ID 正确，且 API 密钥有访问该环境的权限

### Q: 部署失败
**A**: 查看 Actions 日志，通常是权限或配置问题

## 📞 获取帮助

如果遇到问题，可以：

1. 查看 [GitHub Actions 日志](https://github.com/xuyuzeamazon/ai-resume-cloudrun/actions)
2. 检查 [腾讯云控制台](https://console.cloud.tencent.com/tcb) 的部署状态
3. 提交 [Issue](https://github.com/xuyuzeamazon/ai-resume-cloudrun/issues)

---

**⚠️ 重要提醒**: 请妥善保管你的 API 密钥，不要泄露给他人！
