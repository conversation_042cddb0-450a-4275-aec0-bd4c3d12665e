// 验证积木数量显示修复效果
// 检查修复后的代码逻辑是否正确

const fs = require('fs');
const path = require('path');

console.log('🔍 开始验证积木数量显示修复效果...\n');

// 1. 检查 pages/bricks/bricks.js 修复情况
console.log('📁 检查 pages/bricks/bricks.js 修复情况:');
try {
  const bricksJsPath = path.join(__dirname, 'pages/bricks/bricks.js');
  const bricksJsContent = fs.readFileSync(bricksJsPath, 'utf8');
  
  // 检查是否修复了错误的赋值
  const hasCorrectSkillCount = bricksJsContent.includes('skillCount: skillCount, // 修复：显示真正的技能积木数量');
  const hasAbilityCount = bricksJsContent.includes('abilityCount: abilityCount, // 新增：能力积木总数（技能+项目）');
  const hasDataAbilityCount = bricksJsContent.includes('abilityCount: 0, // 能力积木总数（技能+项目）');
  
  console.log('✅ skillCount修复:', hasCorrectSkillCount ? '已修复' : '❌ 未修复');
  console.log('✅ abilityCount添加:', hasAbilityCount ? '已添加' : '❌ 未添加');
  console.log('✅ data中abilityCount:', hasDataAbilityCount ? '已添加' : '❌ 未添加');
  
} catch (error) {
  console.log('❌ 检查bricks.js失败:', error.message);
}

// 2. 检查 pages/bricks/bricks.wxml 修复情况
console.log('\n📁 检查 pages/bricks/bricks.wxml 修复情况:');
try {
  const bricksWxmlPath = path.join(__dirname, 'pages/bricks/bricks.wxml');
  const bricksWxmlContent = fs.readFileSync(bricksWxmlPath, 'utf8');
  
  // 检查是否修改了显示逻辑
  const hasCorrectTitle = bricksWxmlContent.includes('🧱 能力积木 ({{abilityCount}})');
  const hasCorrectCondition = bricksWxmlContent.includes('wx:if="{{!isSelectMode && abilityCount > 0}}"');
  
  console.log('✅ 标题显示修复:', hasCorrectTitle ? '已修复' : '❌ 未修复');
  console.log('✅ 清除按钮条件修复:', hasCorrectCondition ? '已修复' : '❌ 未修复');
  
} catch (error) {
  console.log('❌ 检查bricks.wxml失败:', error.message);
}

// 3. 模拟数据验证修复逻辑
console.log('\n🧪 模拟数据验证修复逻辑:');

// 模拟用户当前的积木数据（根据截图，用户有2个积木但显示7个）
const userBricks = [
  {
    id: 'brick1',
    title: '最终批量测试积木2',
    category: 'skills',
    description: '验证数据一致性'
  },
  {
    id: 'brick2', 
    title: '批量测试积木2',
    category: 'project',
    description: '验证云数据库保存是否正常'
  }
];

// 应用修复后的计数逻辑
function applyFixedCountLogic(bricks) {
  // 技能积木统计
  const skillBricks = bricks.filter(brick =>
    brick.category === 'skills' ||
    brick.category === '技能' ||
    brick.category === '技术能力' ||
    brick.category === 'skill' ||
    brick.category === 'abilities'
  );
  const skillCount = skillBricks.length;
  
  // 项目积木统计
  const projectBricks = bricks.filter(brick =>
    brick.category === 'project' ||
    brick.category === '项目' ||
    brick.category === '项目经验' ||
    brick.category === 'projects'
  );
  const projectCount = projectBricks.length;
  
  // 能力积木总数（修复后的正确计算）
  const abilityCount = skillCount + projectCount;
  
  return {
    totalCount: bricks.length,
    skillCount,
    projectCount,
    abilityCount
  };
}

const result = applyFixedCountLogic(userBricks);

console.log('📊 修复前的问题:');
console.log('- 界面显示: 能力积木(7)');
console.log('- 实际积木: 2个');
console.log('- 问题原因: skillCount被错误赋值为abilityCount');

console.log('\n📊 修复后的结果:');
console.log('- 总积木数:', result.totalCount);
console.log('- 技能积木数:', result.skillCount);
console.log('- 项目积木数:', result.projectCount);
console.log('- 能力积木总数:', result.abilityCount);
console.log('- 界面将显示: 能力积木(' + result.abilityCount + ')');

// 4. 验证修复效果
console.log('\n🎯 修复效果验证:');
const isFixed = result.abilityCount === result.totalCount;
console.log('✅ 数量一致性:', isFixed ? '修复成功' : '仍有问题');
console.log('✅ 显示正确性:', result.abilityCount === 2 ? '显示正确' : '显示错误');

// 5. 生成修复报告
console.log('\n📋 修复报告:');
console.log('🔧 已修复的问题:');
console.log('1. pages/bricks/bricks.js 第922行: skillCount: abilityCount → skillCount: skillCount');
console.log('2. pages/bricks/bricks.js 添加了 abilityCount 字段到 setData');
console.log('3. pages/bricks/bricks.js 在data中添加了 abilityCount: 0');
console.log('4. pages/bricks/bricks.wxml 第209行: {{skillCount}} → {{abilityCount}}');
console.log('5. pages/bricks/bricks.wxml 第217行: skillCount > 0 → abilityCount > 0');

console.log('\n🎯 预期效果:');
console.log('- 前端显示的积木数量与实际积木数量完全一致');
console.log('- 所有计数器组件显示相同的正确数量');
console.log('- 添加/删除积木时计数器实时更新');
console.log('- 页面刷新后计数器显示保持正确');

console.log('\n✅ 积木数量显示异常修复验证完成！');
console.log('🚀 建议在微信开发者工具中测试实际效果');
