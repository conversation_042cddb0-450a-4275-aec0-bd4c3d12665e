# 🚀 AI 简历云托管快速开始指南

## 📋 准备工作

### 1. 环境要求
- Node.js >= 18.0.0
- Docker (可选，用于本地测试)
- Git
- GitHub 账号
- 腾讯云账号

### 2. 获取必要的密钥

#### 腾讯云 API 密钥
1. 登录 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 进入 [访问管理 > API密钥管理](https://console.cloud.tencent.com/cam/capi)
3. 创建或获取 SecretId 和 SecretKey

#### 云开发环境 ID
1. 进入 [云开发控制台](https://console.cloud.tencent.com/tcb)
2. 创建或选择环境
3. 复制环境 ID

## 🛠️ 方法一：一键部署（推荐）

### 1. 克隆仓库
```bash
git clone https://github.com/xuyuzeamazon/ai-resume-cloudrun.git
cd ai-resume-cloudrun
```

### 2. 运行一键部署脚本
```bash
# 设置环境变量（可选）
export CLOUDBASE_ENV_ID="your-env-id"
export CLOUDBASE_SECRET_ID="your-secret-id"
export CLOUDBASE_SECRET_KEY="your-secret-key"

# 运行部署脚本
chmod +x deploy-cloudrun.sh
./deploy-cloudrun.sh
```

脚本会自动：
- ✅ 检查必需的工具
- ✅ 安装 GitHub CLI 和 CloudBase CLI
- ✅ 设置 GitHub Secrets
- ✅ 验证 CloudBase 配置
- ✅ 构建和测试 Docker 镜像
- ✅ 部署到微信云托管

## 🔧 方法二：手动部署

### 1. 设置 GitHub Secrets

在 GitHub 仓库设置中添加：
- `CLOUDBASE_ENV_ID`: 云开发环境 ID
- `CLOUDBASE_SECRET_ID`: 腾讯云 API 密钥 ID
- `CLOUDBASE_SECRET_KEY`: 腾讯云 API 密钥 Key

### 2. 推送代码触发部署
```bash
git add .
git commit -m "feat: 初始化云托管服务"
git push origin main
```

### 3. 查看部署状态
- GitHub Actions: `https://github.com/your-username/ai-resume-cloudrun/actions`
- 云托管控制台: `https://console.cloud.tencent.com/tcb`

## 📱 方法三：本地开发

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 测试 API
```bash
# 健康检查
curl http://localhost:80/health

# 测试截图功能
curl -X POST http://localhost:80/resume-snapshot \
  -H "Content-Type: application/json" \
  -d '{
    "html": "<html><body><h1>测试简历</h1></body></html>",
    "options": {
      "width": 800,
      "height": 600,
      "format": "png"
    }
  }' \
  --output test-screenshot.png
```

## 🐳 Docker 部署

### 1. 构建镜像
```bash
docker build -t resume-snapshot .
```

### 2. 运行容器
```bash
docker run -p 80:80 resume-snapshot
```

### 3. 测试服务
```bash
curl http://localhost:80/health
```

## 🔍 验证部署

### 使用验证脚本
```bash
node verify-deployment.js https://your-service-url.com
```

### 手动验证
1. **健康检查**: `GET /health`
2. **服务信息**: `GET /`
3. **截图功能**: `POST /resume-snapshot`

## 📊 监控和日志

### 查看云托管日志
```bash
# 安装 CloudBase CLI
npm install -g @cloudbase/cli

# 登录
tcb login --apiKeyId YOUR_SECRET_ID --apiKey YOUR_SECRET_KEY

# 查看日志
tcb functions:log --envId YOUR_ENV_ID --name resume-snapshot
```

### GitHub Actions 日志
访问 `https://github.com/your-username/ai-resume-cloudrun/actions` 查看部署日志

## 🐛 常见问题

### Q: 部署失败，提示权限不足
**A**: 检查 API 密钥是否具有云开发和云托管权限

### Q: 截图生成失败
**A**: 检查 HTML 内容是否有效，确保服务器有足够内存

### Q: 服务无法访问
**A**: 检查云托管服务状态，确认端口配置正确

### Q: GitHub Actions 失败
**A**: 检查 Secrets 配置，查看详细错误日志

## 📞 获取帮助

1. **查看文档**: [README.md](./README.md)
2. **提交 Issue**: [GitHub Issues](https://github.com/xuyuzeamazon/ai-resume-cloudrun/issues)
3. **查看示例**: [API 测试示例](./examples/)

## 🎯 下一步

部署成功后，你可以：

1. **集成到主应用**: 将截图服务集成到你的 AI 简历系统
2. **自定义配置**: 修改 Dockerfile 和配置文件
3. **监控优化**: 设置监控和性能优化
4. **扩展功能**: 添加更多图片处理功能

---

**🎉 恭喜！你已经成功部署了 AI 简历云托管截图服务！**
