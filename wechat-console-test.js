/**
 * 微信开发者工具控制台测试脚本
 * 直接复制粘贴到微信开发者工具控制台中运行
 * 验证积木数据持久化修复效果
 */

console.log('🔧 开始验证积木数据持久化修复效果...');
console.log('📱 当前环境:', typeof wx !== 'undefined' ? '✅ 微信小程序' : '❌ 非微信环境');

if (typeof wx === 'undefined') {
  console.error('❌ 请在微信开发者工具控制台中运行此脚本');
} else {
  console.log('✅ 微信环境检测成功，开始测试...\n');
  
  // 测试函数
  async function runFixVerificationTest() {
    try {
      console.log('📋 === 积木数据持久化修复验证测试 ===\n');
      
      // 测试1: 验证BrickManager基本功能
      console.log('🧪 测试1: 验证BrickManager基本功能');
      const { instance: BrickManager } = require('utils/brick-manager.js');
      console.log('✅ BrickManager加载成功');
      
      // 测试2: 获取当前积木数据
      console.log('\n🧪 测试2: 获取当前积木数据');
      const currentBricks = await BrickManager.getBricks();
      console.log(`📊 当前积木数量: ${currentBricks.length}`);
      
      if (currentBricks.length > 0) {
        console.log('📋 积木列表:');
        currentBricks.forEach((brick, index) => {
          console.log(`  ${index + 1}. ${brick.title} (${brick.category})`);
        });
      }
      
      // 测试3: 验证批量保存功能
      console.log('\n🧪 测试3: 验证批量保存功能');
      const testBricks = [
        {
          id: 'console_test_1',
          title: '控制台测试积木1',
          description: '验证批量保存功能',
          content: '这是通过控制台测试创建的积木，用于验证批量保存功能是否正常工作',
          category: 'skill',
          tags: ['控制台测试', '批量保存'],
          usageCount: 0,
          createTime: new Date().toISOString()
        },
        {
          id: 'console_test_2',
          title: '控制台测试积木2',
          description: '验证数据持久化',
          content: '这是第二个测试积木，用于验证数据是否能正确持久化到云数据库',
          category: 'project',
          tags: ['数据持久化', '测试'],
          usageCount: 0,
          createTime: new Date().toISOString()
        }
      ];
      
      console.log(`🔄 开始批量保存${testBricks.length}个测试积木...`);
      await BrickManager.addBricksBatch(testBricks);
      console.log('✅ 批量保存完成');
      
      // 测试4: 验证保存后的数据获取
      console.log('\n🧪 测试4: 验证保存后的数据获取');
      const afterSaveBricks = await BrickManager.getBricks();
      console.log(`📊 保存后积木数量: ${afterSaveBricks.length}`);
      
      const hasNewBricks = afterSaveBricks.some(b => b.id.startsWith('console_test_'));
      console.log(`🔍 包含新测试积木: ${hasNewBricks ? '✅ 是' : '❌ 否'}`);
      
      // 测试5: 验证刷新后数据持久性
      console.log('\n🧪 测试5: 验证刷新后数据持久性');
      console.log('🔄 强制刷新BrickManager...');
      await BrickManager.refresh();
      
      const afterRefreshBricks = await BrickManager.getBricks();
      console.log(`📊 刷新后积木数量: ${afterRefreshBricks.length}`);
      
      const persistenceTest = afterRefreshBricks.some(b => b.id.startsWith('console_test_'));
      console.log(`🔍 数据持久性测试: ${persistenceTest ? '✅ 通过' : '❌ 失败'}`);
      
      // 计算测试结果
      const tests = [
        { name: 'BrickManager加载', result: true },
        { name: '数据获取功能', result: currentBricks.length >= 0 },
        { name: '批量保存功能', result: true },
        { name: '保存后数据验证', result: hasNewBricks },
        { name: '数据持久性测试', result: persistenceTest }
      ];
      
      const passedTests = tests.filter(t => t.result).length;
      const totalTests = tests.length;
      const successRate = (passedTests / totalTests) * 100;
      
      console.log('\n' + '='.repeat(50));
      console.log('📋 测试结果汇总:');
      console.log(`📊 总测试数: ${totalTests}`);
      console.log(`✅ 通过数: ${passedTests}`);
      console.log(`❌ 失败数: ${totalTests - passedTests}`);
      console.log(`🎯 成功率: ${successRate}%`);
      
      console.log('\n📋 详细结果:');
      tests.forEach((test, index) => {
        const status = test.result ? '✅' : '❌';
        console.log(`${index + 1}. ${status} ${test.name}`);
      });
      
      if (successRate >= 95) {
        console.log('\n🎉 恭喜！积木数据持久化修复成功！');
        console.log('✅ bricks.js解析的积木数据现在能正确保存');
        console.log('✅ generate.js现在能正确获取积木数据');
        console.log('✅ 数据在刷新后仍然持久存在');
        console.log('\n🚀 现在可以进行完整的用户流程测试了！');
      } else {
        console.log('\n⚠️ 修复效果不理想，需要进一步调试');
        console.log('🔍 请检查失败的测试项目');
      }
      
      return {
        success: successRate >= 95,
        successRate,
        passedTests,
        totalTests,
        finalBrickCount: afterRefreshBricks.length
      };
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error);
      console.error('🔍 错误详情:', error.message);
      return { success: false, error: error.message };
    }
  }
  
  // 自动运行测试
  console.log('🚀 自动开始测试...\n');
  runFixVerificationTest().then(result => {
    console.log('\n🏁 测试完成！');
    if (result.success) {
      console.log('🎊 修复验证成功！可以继续使用系统了。');
    } else {
      console.log('🔧 需要进一步修复，请检查错误信息。');
    }
  }).catch(error => {
    console.error('💥 测试运行异常:', error);
  });
  
  // 提供手动测试函数
  console.log('\n📖 可用的手动测试函数:');
  console.log('- runFixVerificationTest() // 运行完整验证测试');
  
  // 全局暴露测试函数
  if (typeof global !== 'undefined') {
    global.runFixVerificationTest = runFixVerificationTest;
  }
  if (typeof window !== 'undefined') {
    window.runFixVerificationTest = runFixVerificationTest;
  }
}

// 使用说明
console.log('\n📝 使用说明:');
console.log('1. 确保在微信开发者工具中运行');
console.log('2. 脚本会自动运行测试');
console.log('3. 查看测试结果和成功率');
console.log('4. 如果成功率≥95%，表示修复成功');
console.log('\n⚠️ 重要: 此测试使用真实数据，不使用任何模拟数据');
