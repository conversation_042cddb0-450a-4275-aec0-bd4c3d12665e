/**
 * 测试用户积木数据同步问题
 * 模拟真实用户的简历上传和积木生成过程
 */

const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

// 用户的真实 OPENID
const USER_OPENID = 'o16hGvi6bsSwrpnbtFIg06wVutX4';

async function testUserBrickSync() {
  try {
    console.log('🧪 测试用户积木数据同步问题...');
    console.log('👤 用户OPENID:', USER_OPENID);
    
    // 1. 首先查询用户当前的积木数据
    console.log('\n📋 1. 查询用户当前积木数据...');
    const currentBricks = await queryUserBricks();
    console.log(`✅ 用户当前有 ${currentBricks.length} 个积木`);
    
    // 2. 模拟简历解析生成新积木（使用真实用户身份）
    console.log('\n🤖 2. 模拟简历解析生成新积木...');
    const newBricks = await simulateResumeParseWithUserContext();
    
    // 3. 验证新积木是否正确保存
    console.log('\n🔍 3. 验证新积木是否正确保存...');
    const updatedBricks = await queryUserBricks();
    console.log(`✅ 用户现在有 ${updatedBricks.length} 个积木`);
    
    // 4. 测试积木库页面的数据加载
    console.log('\n📱 4. 测试积木库页面数据加载...');
    await testBrickManagerLoad();
    
    console.log('\n🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 查询用户的积木数据
 */
async function queryUserBricks() {
  try {
    const db = app.database();
    const result = await db.collection('bricks')
      .where({
        _openid: USER_OPENID
      })
      .get();
    
    return result.data || [];
  } catch (error) {
    console.error('❌ 查询用户积木失败:', error);
    return [];
  }
}

/**
 * 模拟简历解析，使用真实用户身份
 */
async function simulateResumeParseWithUserContext() {
  try {
    console.log('📄 模拟简历解析过程...');
    
    // 创建模拟的用户上下文
    const mockContext = {
      OPENID: USER_OPENID,
      APPID: 'your-app-id',
      UNIONID: 'mock-union-id'
    };
    
    // 模拟简历内容
    const resumeContent = `
张三
电话：13800138000
邮箱：<EMAIL>

工作经历：
2020-2024 阿里巴巴 高级前端工程师
- 负责电商平台前端开发
- 使用React、Vue.js开发用户界面
- 优化页面性能，提升用户体验

教育背景：
2016-2020 清华大学 计算机科学与技术 本科

技能：
JavaScript、React、Vue.js、Node.js、Python
    `;
    
    // 直接调用 resumeWorker 云函数，传递用户上下文
    console.log('🚀 调用 resumeWorker 云函数...');
    
    // 注意：这里我们需要设置环境变量来模拟用户身份
    process.env.TEST_USER_OPENID = USER_OPENID;
    
    const result = await app.callFunction({
      name: 'resumeWorker',
      data: {
        resumeContent: resumeContent,
        fileName: '张三简历.txt',
        fileType: 'txt'
      }
    });
    
    console.log('📊 云函数调用结果:', {
      success: result.result?.success,
      bricksCount: result.result?.data?.bricks?.length || 0
    });
    
    return result.result?.data?.bricks || [];
    
  } catch (error) {
    console.error('❌ 模拟简历解析失败:', error);
    return [];
  }
}

/**
 * 测试 brickManager 云函数的数据加载
 */
async function testBrickManagerLoad() {
  try {
    console.log('🔧 测试 brickManager 云函数...');
    
    // 注意：brickManager 需要微信小程序环境的身份验证
    // 这里我们直接查询数据库来验证数据
    const bricks = await queryUserBricks();
    
    console.log('📋 积木库数据验证:');
    console.log(`- 总积木数: ${bricks.length}`);
    
    // 按分类统计
    const categories = {};
    bricks.forEach(brick => {
      const category = brick.category || 'unknown';
      categories[category] = (categories[category] || 0) + 1;
    });
    
    console.log('- 分类统计:', categories);
    
    // 显示最新的几个积木
    const recentBricks = bricks
      .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
      .slice(0, 3);
    
    console.log('- 最新积木:');
    recentBricks.forEach((brick, index) => {
      console.log(`  ${index + 1}. ${brick.title} (${brick.category})`);
    });
    
  } catch (error) {
    console.error('❌ 测试 brickManager 失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testUserBrickSync().then(() => {
    console.log('🏁 测试脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('💥 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testUserBrickSync,
  queryUserBricks,
  simulateResumeParseWithUserContext,
  testBrickManagerLoad
};
