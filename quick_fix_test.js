/**
 * 快速修复测试脚本
 * 在微信开发者工具的Console中运行此脚本来快速修复个人信息显示问题
 */

// 快速修复个人信息显示问题
async function quickFixPersonalInfo() {
  console.log('🚀 开始快速修复个人信息显示问题...');
  
  try {
    // 1. 获取BrickManager
    const BrickManager = require('utils/brick-manager.js');
    
    // 2. 强制重新生成默认积木
    console.log('🔄 强制重新生成默认积木...');
    const newBricks = await BrickManager.forceRegenerateDefaults();
    
    console.log('✅ 重新生成完成，积木数量:', newBricks.length);
    
    // 3. 检查生成的积木
    const personalBricks = newBricks.filter(brick => brick.category === 'personal');
    console.log('👤 个人信息积木数量:', personalBricks.length);
    
    if (personalBricks.length > 0) {
      const brick = personalBricks[0];
      console.log('📋 个人信息积木详情:');
      console.log('- 标题:', brick.title);
      console.log('- 描述:', brick.description);
      console.log('- 分类:', brick.category);
      
      // 验证是否修复成功
      if (brick.title !== '暂无积木数据' && brick.description !== '暂无积木数据') {
        console.log('✅ 积木数据修复成功！');
      } else {
        console.log('❌ 积木数据仍有问题');
        return false;
      }
    }
    
    // 4. 刷新页面显示
    console.log('🔄 刷新页面显示...');
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route === 'pages/bricks/bricks') {
      // 直接更新页面数据
      currentPage.setData({
        bricks: newBricks,
        filteredBricks: newBricks
      });
      
      // 更新统计
      currentPage.updateCounts();
      currentPage.filterBricks();
      
      console.log('✅ 页面数据已刷新');
      
      // 验证页面显示
      setTimeout(() => {
        const pageBricks = currentPage.data.bricks || [];
        const pagePersonalBricks = pageBricks.filter(brick => brick.category === 'personal');
        
        if (pagePersonalBricks.length > 0) {
          const pageBrick = pagePersonalBricks[0];
          console.log('📱 页面显示验证:');
          console.log('- 页面标题:', pageBrick.title);
          console.log('- 页面描述:', pageBrick.description);
          
          if (pageBrick.title !== '暂无积木数据' && pageBrick.description !== '暂无积木数据') {
            console.log('🎉 页面显示修复成功！个人信息模块应该正常显示了');
            return true;
          } else {
            console.log('❌ 页面显示仍有问题');
            return false;
          }
        } else {
          console.log('⚠️ 页面中未找到个人信息积木');
          return false;
        }
      }, 500);
      
    } else {
      console.log('⚠️ 当前不在积木库页面，请先导航到积木库页面');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 快速修复失败:', error);
    return false;
  }
}

// 检查当前状态
function checkCurrentStatus() {
  console.log('🔍 检查当前个人信息显示状态...');
  
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route === 'pages/bricks/bricks') {
      const bricks = currentPage.data.bricks || [];
      const personalBricks = bricks.filter(brick => brick.category === 'personal');
      
      console.log('📊 当前状态:');
      console.log('- 总积木数:', bricks.length);
      console.log('- 个人信息积木数:', personalBricks.length);
      
      if (personalBricks.length > 0) {
        const brick = personalBricks[0];
        console.log('- 个人信息标题:', brick.title);
        console.log('- 个人信息描述:', brick.description);
        
        if (brick.title === '暂无积木数据' || brick.description === '暂无积木数据') {
          console.log('❌ 检测到"暂无积木数据"问题');
          return false;
        } else {
          console.log('✅ 个人信息显示正常');
          return true;
        }
      } else {
        console.log('⚠️ 未找到个人信息积木');
        return false;
      }
    } else {
      console.log('⚠️ 当前不在积木库页面');
      return false;
    }
  } catch (error) {
    console.error('❌ 状态检查失败:', error);
    return false;
  }
}

// 一键修复
async function oneClickFix() {
  console.log('🔧 开始一键修复...');
  
  // 1. 检查当前状态
  const isOk = checkCurrentStatus();
  
  if (!isOk) {
    console.log('🚨 检测到问题，开始修复...');
    
    // 2. 执行快速修复
    const success = await quickFixPersonalInfo();
    
    if (success) {
      console.log('🎉 一键修复完成！');
    } else {
      console.log('❌ 一键修复失败');
    }
  } else {
    console.log('✅ 当前状态正常，无需修复');
  }
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    quickFixPersonalInfo,
    checkCurrentStatus,
    oneClickFix
  };
}

console.log('🔧 快速修复脚本已加载');
console.log('💡 使用方法:');
console.log('1. 运行 checkCurrentStatus() 检查当前状态');
console.log('2. 运行 quickFixPersonalInfo() 快速修复');
console.log('3. 运行 oneClickFix() 一键检查并修复');
console.log('');
console.log('🚀 推荐直接运行: oneClickFix()');
