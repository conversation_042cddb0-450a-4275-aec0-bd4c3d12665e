name: 微信云托管自动部署

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  CLOUDBASE_ENV_ID: ${{ secrets.CLOUDBASE_ENV_ID }}
  CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
  CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
  SERVICE_NAME: resume-snapshot

jobs:
  test:
    name: 运行测试
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行 ESLint
      run: npm run lint
      continue-on-error: true
      
    - name: 运行测试
      run: npm test
      continue-on-error: true

  build:
    name: 构建 Docker 镜像
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 构建 Docker 镜像
      run: |
        docker build -t ${{ env.SERVICE_NAME }}:${{ github.sha }} .
        docker tag ${{ env.SERVICE_NAME }}:${{ github.sha }} ${{ env.SERVICE_NAME }}:latest
        
    - name: 测试 Docker 镜像
      run: |
        # 启动容器
        docker run -d --name test-container -p 8080:80 ${{ env.SERVICE_NAME }}:latest
        
        # 等待服务启动
        sleep 15
        
        # 健康检查
        curl -f http://localhost:8080/health || exit 1
        
        # 测试基本功能
        curl -f http://localhost:8080/ || exit 1
        curl -f http://localhost:8080/ping || exit 1
        
        # 清理
        docker stop test-container
        docker rm test-container
        
    - name: 保存 Docker 镜像
      run: |
        docker save ${{ env.SERVICE_NAME }}:latest | gzip > ${{ env.SERVICE_NAME }}.tar.gz
        
    - name: 上传构建产物
      uses: actions/upload-artifact@v4
      with:
        name: docker-image
        path: ${{ env.SERVICE_NAME }}.tar.gz
        retention-days: 1

  deploy:
    name: 部署到微信云托管
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 下载构建产物
      uses: actions/download-artifact@v4
      with:
        name: docker-image
        
    - name: 加载 Docker 镜像
      run: |
        docker load < ${{ env.SERVICE_NAME }}.tar.gz
        docker images
        
    - name: 安装 CloudBase CLI
      run: |
        npm install -g @cloudbase/cli@latest
        
    - name: 验证环境变量
      run: |
        if [ -z "${{ secrets.CLOUDBASE_ENV_ID }}" ]; then
          echo "❌ CLOUDBASE_ENV_ID 未设置"
          exit 1
        fi
        if [ -z "${{ secrets.CLOUDBASE_SECRET_ID }}" ]; then
          echo "❌ CLOUDBASE_SECRET_ID 未设置"
          exit 1
        fi
        if [ -z "${{ secrets.CLOUDBASE_SECRET_KEY }}" ]; then
          echo "❌ CLOUDBASE_SECRET_KEY 未设置"
          exit 1
        fi
        echo "✅ 所有必需的环境变量已设置"
        
    - name: 配置 CloudBase 认证
      run: |
        tcb login --apiKeyId ${{ secrets.CLOUDBASE_SECRET_ID }} --apiKey ${{ secrets.CLOUDBASE_SECRET_KEY }}
        
    - name: 验证 CloudBase 配置
      run: |
        tcb env:list
        
    - name: 部署到微信云托管
      run: |
        echo "🚀 开始部署到微信云托管..."
        tcb framework deploy --envId ${{ secrets.CLOUDBASE_ENV_ID }} --verbose
        
    - name: 部署后验证
      run: |
        echo "⏳ 等待服务启动..."
        sleep 60
        
        # 这里可以添加部署后的验证逻辑
        echo "✅ 部署完成！"
        echo "📍 请在微信云托管控制台查看服务状态"
        echo "🔗 控制台地址: https://console.cloud.tencent.com/tcb"
        
    - name: 发送部署通知
      if: always()
      run: |
        if [ ${{ job.status }} == 'success' ]; then
          echo "✅ 🎉 部署成功！"
          echo "📦 版本: ${{ github.sha }}"
          echo "🌐 环境: ${{ secrets.CLOUDBASE_ENV_ID }}"
          echo "⏰ 时间: $(date)"
        else
          echo "❌ 💥 部署失败！"
          echo "📦 版本: ${{ github.sha }}"
          echo "🔍 请检查日志获取详细信息"
        fi
