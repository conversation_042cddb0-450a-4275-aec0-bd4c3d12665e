/**
 * 高性能缓存管理器
 * 解决微信小程序缓存性能极慢问题，确保每页缓存时间不超过10秒
 */

class CacheManager {
  constructor() {
    this.cache = new Map();
    this.cacheTimestamps = new Map();
    this.loadingPromises = new Map(); // 防止重复请求

    // 缓存配置
    this.config = {
      defaultTTL: 300000, // 5分钟默认过期时间
      maxCacheSize: 100,  // 最大缓存条目数
      performanceTarget: 10000, // 10秒性能目标

      // 不同数据类型的TTL配置
      ttlConfig: {
        userInfo: 1800000,    // 用户信息30分钟
        bricks: 600000,       // 积木数据10分钟
        templates: 1800000,   // 模板数据30分钟
        stats: 300000,        // 统计数据5分钟
        resumes: 600000,      // 简历数据10分钟
        systemInfo: 3600000   // 系统信息1小时
      }
    };

    // 性能监控
    this.performanceMetrics = {
      hits: 0,
      misses: 0,
      totalRequests: 0,
      averageLoadTime: 0,
      slowQueries: []
    };
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @param {Function} loader - 数据加载函数
   * @param {Object} options - 选项
   * @returns {Promise} 缓存数据
   */
  async get(key, loader, options = {}) {
    const startTime = performance.now();
    this.performanceMetrics.totalRequests++;

    try {
      // 检查是否有正在进行的加载
      if (this.loadingPromises.has(key)) {
        console.log(`⏳ 等待正在进行的加载: ${key}`);
        const result = await this.loadingPromises.get(key);
        this.recordPerformance(key, startTime, true);
        return result;
      }

      // 检查缓存是否有效
      if (this.isValid(key, options.ttl)) {
        console.log(`✅ 缓存命中: ${key}`);
        this.performanceMetrics.hits++;
        this.recordPerformance(key, startTime, true);
        return this.cache.get(key);
      }

      // 缓存未命中，需要加载数据
      console.log(`📡 缓存未命中，加载数据: ${key}`);
      this.performanceMetrics.misses++;

      // 创建加载Promise并缓存，防止重复请求
      const loadingPromise = this.loadWithTimeout(key, loader, options);
      this.loadingPromises.set(key, loadingPromise);

      try {
        const data = await loadingPromise;
        this.set(key, data, options.ttl);
        this.recordPerformance(key, startTime, false);
        return data;
      } finally {
        this.loadingPromises.delete(key);
      }

    } catch (error) {
      console.error(`❌ 缓存获取失败: ${key}`, error);
      this.recordPerformance(key, startTime, false, error);

      // 返回过期缓存作为降级方案
      if (this.cache.has(key)) {
        console.log(`🔄 使用过期缓存作为降级方案: ${key}`);
        return this.cache.get(key);
      }

      throw error;
    }
  }

  /**
   * 带超时的数据加载
   */
  async loadWithTimeout(key, loader, options) {
    const timeout = options.timeout || this.config.performanceTarget;

    return new Promise(async (resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        reject(new Error(`数据加载超时: ${key} (${timeout}ms)`));
      }, timeout);

      try {
        const data = await loader();
        clearTimeout(timeoutId);
        resolve(data);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 数据
   * @param {number} ttl - 生存时间（毫秒）
   */
  set(key, data, ttl) {
    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxCacheSize) {
      this.evictOldest();
    }

    const finalTTL = ttl || this.getTTLForKey(key);

    this.cache.set(key, data);
    this.cacheTimestamps.set(key, {
      timestamp: Date.now(),
      ttl: finalTTL
    });

    console.log(`💾 缓存已设置: ${key} (TTL: ${finalTTL}ms)`);
  }

  /**
   * 检查缓存是否有效
   */
  isValid(key, customTTL) {
    if (!this.cache.has(key) || !this.cacheTimestamps.has(key)) {
      return false;
    }

    const { timestamp, ttl } = this.cacheTimestamps.get(key);
    const finalTTL = customTTL || ttl;
    const age = Date.now() - timestamp;

    return age < finalTTL;
  }

  /**
   * 获取键的默认TTL
   */
  getTTLForKey(key) {
    for (const [type, ttl] of Object.entries(this.config.ttlConfig)) {
      if (key.includes(type)) {
        return ttl;
      }
    }
    return this.config.defaultTTL;
  }

  /**
   * 淘汰最旧的缓存项
   */
  evictOldest() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, { timestamp }] of this.cacheTimestamps.entries()) {
      if (timestamp < oldestTime) {
        oldestTime = timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
      console.log(`🗑️ 淘汰最旧缓存: ${oldestKey}`);
    }
  }

  /**
   * 删除缓存项
   */
  delete(key) {
    this.cache.delete(key);
    this.cacheTimestamps.delete(key);
    this.loadingPromises.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
    this.cacheTimestamps.clear();
    this.loadingPromises.clear();
    console.log('🧹 所有缓存已清空');
  }

  /**
   * 预加载关键数据
   */
  async preloadCriticalData() {
    console.log('🚀 开始预加载关键数据...');

    const preloadTasks = [
      {
        key: 'userInfo',
        loader: () => this.loadUserInfo(),
        priority: 1
      },
      {
        key: 'bricks',
        loader: () => this.loadBricks(),
        priority: 2
      },
      {
        key: 'templates',
        loader: () => this.loadTemplates(),
        priority: 3
      }
    ];

    // 按优先级并发加载
    const promises = preloadTasks.map(task =>
      this.get(task.key, task.loader, { timeout: 5000 })
        .catch(error => {
          console.warn(`⚠️ 预加载失败: ${task.key}`, error);
          return null;
        })
    );

    const results = await Promise.allSettled(promises);
    const successCount = results.filter(r => r.status === 'fulfilled').length;

    console.log(`✅ 预加载完成: ${successCount}/${preloadTasks.length} 成功`);
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      const isLoggedIn = wx.getStorageSync('isLoggedIn');

      if (userInfo && isLoggedIn) {
        return { userInfo, isLoggedIn };
      }

      return null;
    } catch (error) {
      console.error('加载用户信息失败:', error);
      return null;
    }
  }

  /**
   * 加载积木数据 - 使用统一的BrickManager
   */
  async loadBricks() {
    try {
      // 使用统一的积木管理器实例
      const { instance: BrickManager } = require('./brick-manager.js');
      return await BrickManager.getBricks();
    } catch (error) {
      console.error('加载积木数据失败:', error);
      return [];
    }
  }

  /**
   * 加载模板数据
   */
  async loadTemplates() {
    try {
      const localTemplates = wx.getStorageSync('templates');
      if (localTemplates && localTemplates.length > 0) {
        return localTemplates;
      }

      // 模拟模板数据（实际应该从服务器加载）
      const defaultTemplates = [
        { id: 1, name: '经典模板', type: 'classic' },
        { id: 2, name: '现代模板', type: 'modern' },
        { id: 3, name: '简约模板', type: 'minimal' }
      ];

      wx.setStorageSync('templates', defaultTemplates);
      return defaultTemplates;
    } catch (error) {
      console.error('加载模板数据失败:', error);
      return [];
    }
  }

  /**
   * 记录性能指标
   */
  recordPerformance(key, startTime, isHit, error = null) {
    const duration = performance.now() - startTime;

    // 更新平均加载时间
    const totalTime = this.performanceMetrics.averageLoadTime * (this.performanceMetrics.totalRequests - 1);
    this.performanceMetrics.averageLoadTime = (totalTime + duration) / this.performanceMetrics.totalRequests;

    // 记录慢查询
    if (duration > 3000) { // 超过3秒的查询
      this.performanceMetrics.slowQueries.push({
        key,
        duration,
        timestamp: Date.now(),
        error: error?.message
      });

      // 只保留最近10个慢查询
      if (this.performanceMetrics.slowQueries.length > 10) {
        this.performanceMetrics.slowQueries.shift();
      }
    }

    console.log(`⏱️ 缓存性能: ${key} - ${duration.toFixed(2)}ms ${isHit ? '(命中)' : '(未命中)'}`);
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const hitRate = this.performanceMetrics.totalRequests > 0
      ? (this.performanceMetrics.hits / this.performanceMetrics.totalRequests * 100).toFixed(2)
      : 0;

    return {
      ...this.performanceMetrics,
      hitRate: `${hitRate}%`,
      cacheSize: this.cache.size,
      averageLoadTime: `${this.performanceMetrics.averageLoadTime.toFixed(2)}ms`
    };
  }

  /**
   * 优化缓存性能
   */
  optimizePerformance() {
    console.log('🔧 优化缓存性能...');

    // 清理过期缓存
    let cleanedCount = 0;
    for (const [key] of this.cacheTimestamps.entries()) {
      if (!this.isValid(key)) {
        this.delete(key);
        cleanedCount++;
      }
    }

    console.log(`🧹 清理了 ${cleanedCount} 个过期缓存项`);

    // 预加载关键数据
    this.preloadCriticalData().catch(error => {
      console.warn('预加载失败:', error);
    });
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager();

module.exports = cacheManager;
