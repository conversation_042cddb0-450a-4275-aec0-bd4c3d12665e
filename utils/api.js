/**
 * API服务 - 统一API调用接口
 * 基于http-api.js，提供全局ApiService
 */

// 引入基础HTTP服务
const httpApi = require('./http-api.js');

// 创建API服务对象
const ApiService = {
  // 基础HTTP方法
  request: httpApi.request,
  get: httpApi.get,
  post: httpApi.post,

  // 用户相关API
  login: async (loginData) => {
    // 使用TCB云函数进行登录
    return await ApiService.loginWithTCB(loginData);
  },

  // 使用TCB云函数登录的方法 - 增强版本，包含重试机制
  loginWithTCB: async (loginData, retryCount = 0) => {
    const maxRetries = 3;
    const retryDelay = Math.pow(2, retryCount) * 1000; // 指数退避：1s, 2s, 4s

    console.log(`🔐 开始TCB云函数登录 (尝试 ${retryCount + 1}/${maxRetries + 1})`, loginData);

    try {
      // 检查网络状态（仅在微信环境中）
      let networkType = 'unknown';
      if (typeof wx !== 'undefined' && wx.getNetworkType) {
        networkType = await new Promise((resolve) => {
          wx.getNetworkType({
            success: (res) => resolve(res.networkType),
            fail: () => resolve('unknown')
          });
        });
      }

      if (networkType === 'none') {
        throw new Error('网络连接不可用，请检查网络设置');
      }

      // 使用TCB云函数调用
      console.log(`🔐 调用TCB云函数userLogin (尝试 ${retryCount + 1}/${maxRetries + 1})`, {
        networkType,
        functionName: 'userLogin'
      });

      // 构造正确的请求数据，添加action参数
      const requestData = {
        action: 'wechat-login',
        code: loginData.code,
        userInfo: loginData.userInfo
      };

      const result = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'userLogin',
          data: requestData,
          success: resolve,
          fail: reject
        });
      });

      console.log('✅ TCB云函数调用成功', result);

      // 正确解析TCB云函数返回的结果
      if (result.result) {
        // 检查是否有statusCode和body（HTTP格式响应）
        if (result.result.statusCode === 200 && result.result.body) {
          try {
            const bodyData = JSON.parse(result.result.body);
            console.log('📄 解析TCB响应body:', bodyData);

            if (bodyData.success && bodyData.data) {
              return bodyData;
            } else {
              const errorMsg = bodyData.error || '登录失败';
              console.error('❌ TCB云函数业务逻辑失败:', errorMsg);
              throw new Error(errorMsg);
            }
          } catch (parseError) {
            console.error('❌ 解析TCB响应失败:', parseError);
            throw new Error('响应格式错误');
          }
        }
        // 检查直接的success格式
        else if (result.result.success) {
          return result.result;
        }
        // 其他错误情况
        else {
          const errorMsg = result.result.message || result.result.error || '登录失败';
          console.error('❌ TCB云函数登录失败:', errorMsg);

          // 对于服务器错误，可以重试
          if (retryCount < maxRetries && (errorMsg.includes('timeout') || errorMsg.includes('网络'))) {
            console.warn(`网络错误，${retryDelay}ms后进行第${retryCount + 1}次重试`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return ApiService.loginWithTCB(loginData, retryCount + 1);
          }

          throw new Error(errorMsg);
        }
      } else {
        throw new Error('TCB云函数调用失败：无返回结果');
      }
    } catch (error) {
      console.error('❌ TCB云函数调用失败:', error);

      // 网络超时重试
      if (error.errMsg && error.errMsg.includes('timeout') && retryCount < maxRetries) {
        console.warn(`网络超时，${retryDelay}ms后进行第${retryCount + 1}次重试`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return ApiService.loginWithTCB(loginData, retryCount + 1);
      }

      // 网络连接失败重试
      if (error.errMsg && error.errMsg.includes('fail') && !error.errMsg.includes('timeout') && retryCount < maxRetries) {
        console.warn(`网络连接失败，${retryDelay}ms后进行第${retryCount + 1}次重试`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return ApiService.loginWithTCB(loginData, retryCount + 1);
      }

      // 提供更友好的错误信息
      if (error.errMsg && error.errMsg.includes('timeout')) {
        throw new Error(`登录请求超时，已重试${retryCount}次。请检查网络连接或稍后重试`);
      } else if (error.errMsg && error.errMsg.includes('fail')) {
        throw new Error(`网络连接失败，已重试${retryCount}次。请检查网络设置或稍后重试`);
      } else {
        throw new Error(error.message || '登录失败，请重试');
      }
    }
  },

  verifyToken: async (token) => {
    return await httpApi.post('/auth/verify', { token });
  },

  getUserStats: async () => {
    return await httpApi.getUserStats();
  },

  // 简历相关API
  parseResume: async (fileID, fileName) => {
    return await httpApi.post('/resume-parse', { fileID, fileName });
  },

  getResumeParseStatus: async (taskId) => {
    return await httpApi.post('/resume-parse-status', { taskId });
  },

  generateResume: async (data) => {
    return await httpApi.post('/resume-generate', data);
  },

  // 积木相关API
  saveBricks: async (bricks, userId) => {
    return await httpApi.post('/bricks-manage', { action: 'save', bricks, userId });
  },

  getBricks: async (userId) => {
    return await httpApi.post('/bricks-manage', { action: 'get', userId });
  },

  // 获取积木列表 - 使用统一的BrickManager
  getBricksList: async (options = {}) => {
    console.log('📦 获取积木列表', options);

    try {
      // 使用统一的积木管理器实例
      const { instance: BrickManager } = require('./brick-manager.js');
      const bricks = await BrickManager.getBricks();

      console.log(`✅ 通过BrickManager获取积木列表: ${bricks.length} 个积木`);

      // 应用过滤选项
      let filteredBricks = bricks;
      const { category, limit = 50, skip = 0 } = options;

      if (category && category !== 'all') {
        filteredBricks = bricks.filter(brick => brick.category === category);
      }

      // 应用分页
      const startIndex = skip;
      const endIndex = startIndex + limit;
      const paginatedBricks = filteredBricks.slice(startIndex, endIndex);

      return {
        success: true,
        data: paginatedBricks,
        total: filteredBricks.length,
        source: 'brick-manager'
      };

    } catch (error) {
      console.error('❌ 获取积木列表失败:', error);

      return {
        success: false,
        data: [],
        total: 0,
        source: 'error',
        error: error.message
      };
    }
  },

  // 获取默认积木数据 - 已移除技能特长默认积木
  getDefaultBricks: () => {
    return [
      {
        id: 'default_1',
        title: '个人信息',
        category: 'personal',
        content: '姓名、联系方式、邮箱等基本信息',
        type: 'text',
        createTime: new Date().toISOString(),
        isDefault: true
      },
      {
        id: 'default_2',
        title: '教育背景',
        category: 'education',
        content: '学历、专业、毕业院校等教育信息',
        type: 'text',
        createTime: new Date().toISOString(),
        isDefault: true
      },
      {
        id: 'default_3',
        title: '工作经验',
        category: 'experience',
        content: '工作经历、职责描述、成就等',
        type: 'text',
        createTime: new Date().toISOString(),
        isDefault: true
      },
      // 已移除 default_4 技能特长积木 - 技能信息通过项目积木的能力标签体现
      {
        id: 'default_5',
        title: '项目经验',
        category: 'projects',
        content: '参与项目、项目描述、技术栈等',
        type: 'text',
        createTime: new Date().toISOString(),
        isDefault: true
      }
    ];
  },

  // JD分析API
  analyzeJD: async (jdText, companyInfo) => {
    return await httpApi.post('/jd-analyzer', { jdText, companyInfo });
  }
};

// 注册为全局服务
global.ApiService = ApiService;

// 导出服务
module.exports = ApiService;