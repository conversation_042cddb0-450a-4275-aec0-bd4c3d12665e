/**
 * 全局状态管理器
 * 统一管理小程序的全局状态，包括用户数据、简历数据、能力积木等
 */

class Store {
  constructor() {
    this.app = getApp()
    this.listeners = new Map() // 状态监听器

    // 初始化状态
    this.state = {
      // 用户相关状态
      user: {
        isLoggedIn: false,
        userInfo: null,
        openid: null
      },

      // 能力积木相关状态
      bricks: {
        list: [],           // 能力积木列表
        total: 0,           // 总数量
        loading: false,     // 加载状态
        lastUpdated: null   // 最后更新时间
      },

      // 简历相关状态
      resumes: {
        list: [],           // 简历列表
        current: null,      // 当前编辑的简历
        loading: false,     // 加载状态
        lastUpdated: null   // 最后更新时间
      },

      // 生成相关状态
      generation: {
        company: '',        // 目标公司
        position: '',       // 目标职位
        jd: '',            // 职位描述
        jdType: 'text',    // JD输入类型：text/image
        isGenerating: false, // 是否正在生成
        progress: 0        // 生成进度
      },

      // 应用设置状态
      settings: {
        theme: 'light',    // 主题：light/dark
        language: 'zh',    // 语言
        autoSave: true,    // 自动保存
        notifications: true // 通知开关
      },

      // UI状态
      ui: {
        activeTab: 0,      // 当前活跃的tab
        pageLoading: false, // 页面加载状态
        networkStatus: 'unknown' // 网络状态
      }
    }

    this.initializeFromStorage()
  }

  /**
   * 从本地存储初始化状态
   */
  initializeFromStorage() {
    try {
      // 用户状态
      const userInfo = wx.getStorageSync('userInfo')
      const isLoggedIn = wx.getStorageSync('isLoggedIn')
      const openid = wx.getStorageSync('openid')

      if (userInfo && isLoggedIn) {
        this.setState('user', {
          isLoggedIn: true,
          userInfo,
          openid
        })
      }

      // 能力积木状态
      const bricks = wx.getStorageSync('bricks') || []
      this.setState('bricks.list', bricks)
      this.setState('bricks.total', bricks.length)

      // 简历状态
      const resumes = wx.getStorageSync('resumes') || []
      this.setState('resumes.list', resumes)

      // 应用设置
      const settings = wx.getStorageSync('settings')
      if (settings) {
        this.setState('settings', { ...this.state.settings, ...settings })
      }

      console.log('✅ 状态管理器初始化完成')
    } catch (error) {
      console.error('❌ 从存储初始化状态失败:', error)
    }
  }

  /**
   * 设置状态
   * @param {string} path - 状态路径，支持点分隔符
   * @param {any} value - 新值
   * @param {boolean} persist - 是否持久化到本地存储
   */
  setState(path, value, persist = true) {
    const keys = path.split('.')
    let current = this.state

    // 导航到目标对象
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {}
      }
      current = current[keys[i]]
    }

    const finalKey = keys[keys.length - 1]
    const oldValue = current[finalKey]
    current[finalKey] = value

    // 持久化关键状态
    if (persist) {
      this.persistState(keys[0])
    }

    // 通知监听器
    this.notifyListeners(path, value, oldValue)

    // 同步到app.globalData
    this.syncToGlobalData(keys[0])
  }

  /**
   * 获取状态
   * @param {string} path - 状态路径
   */
  getState(path) {
    const keys = path.split('.')
    let current = this.state

    for (const key of keys) {
      if (current === null || current === undefined) {
        return undefined
      }
      current = current[key]
    }

    return current
  }

  /**
   * 订阅状态变化
   * @param {string} path - 状态路径
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消订阅函数
   */
  subscribe(path, callback) {
    if (!this.listeners.has(path)) {
      this.listeners.set(path, new Set())
    }

    this.listeners.get(path).add(callback)

    // 返回取消订阅函数
    return () => {
      const pathListeners = this.listeners.get(path)
      if (pathListeners) {
        pathListeners.delete(callback)
        if (pathListeners.size === 0) {
          this.listeners.delete(path)
        }
      }
    }
  }

  /**
   * 通知监听器
   * @param {string} path - 状态路径
   * @param {any} newValue - 新值
   * @param {any} oldValue - 旧值
   */
  notifyListeners(path, newValue, oldValue) {
    // 通知精确路径的监听器
    const pathListeners = this.listeners.get(path)
    if (pathListeners) {
      pathListeners.forEach(callback => {
        try {
          callback(newValue, oldValue, path)
        } catch (error) {
          console.error('状态监听器执行错误:', error)
        }
      })
    }

    // 通知父路径的监听器
    const parentPaths = this.getParentPaths(path)
    parentPaths.forEach(parentPath => {
      const parentListeners = this.listeners.get(parentPath)
      if (parentListeners) {
        const parentValue = this.getState(parentPath)
        parentListeners.forEach(callback => {
          try {
            callback(parentValue, undefined, parentPath)
          } catch (error) {
            console.error('父路径状态监听器执行错误:', error)
          }
        })
      }
    })
  }

  /**
   * 获取父路径列表
   * @param {string} path - 路径
   */
  getParentPaths(path) {
    const parts = path.split('.')
    const parentPaths = []

    for (let i = parts.length - 1; i > 0; i--) {
      parentPaths.push(parts.slice(0, i).join('.'))
    }

    return parentPaths
  }

  /**
   * 持久化状态到本地存储
   * @param {string} category - 状态分类
   */
  persistState(category) {
    try {
      const stateData = this.state[category]

      switch (category) {
        case 'user':
          if (stateData.userInfo) {
            wx.setStorageSync('userInfo', stateData.userInfo)
            wx.setStorageSync('isLoggedIn', stateData.isLoggedIn)
            wx.setStorageSync('openid', stateData.openid)
          }
          break

        case 'bricks':
          wx.setStorageSync('bricks', stateData.list)
          break

        case 'resumes':
          wx.setStorageSync('resumes', stateData.list)
          break

        case 'settings':
          wx.setStorageSync('settings', stateData)
          break
      }
    } catch (error) {
      console.error(`持久化${category}状态失败:`, error)
    }
  }

  /**
   * 同步状态到app.globalData
   * @param {string} category - 状态分类
   */
  syncToGlobalData(category) {
    if (!this.app) return

    const stateData = this.state[category]

    switch (category) {
      case 'user':
        this.app.globalData.userInfo = stateData.userInfo
        this.app.globalData.isLoggedIn = stateData.isLoggedIn
        break

      case 'bricks':
        this.app.globalData.bricks = stateData.list
        break

      case 'resumes':
        this.app.globalData.resumes = stateData.list
        this.app.globalData.currentResume = stateData.current
        break

      case 'settings':
        this.app.globalData.theme = stateData.theme
        break
    }
  }

  // ===== 用户相关方法 =====

  /**
   * 用户登录
   * @param {Object} userInfo - 用户信息
   * @param {string} openid - 用户openid
   */
  login(userInfo, openid = null) {
    this.setState('user', {
      isLoggedIn: true,
      userInfo,
      openid
    })
    console.log('✅ 用户登录状态已更新')
  }

  /**
   * 用户登出
   */
  logout() {
    this.setState('user', {
      isLoggedIn: false,
      userInfo: null,
      openid: null
    })

    // 清除相关本地存储
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('isLoggedIn')
    wx.removeStorageSync('openid')

    console.log('✅ 用户已登出')
  }

  // ===== 能力积木相关方法 =====

  /**
   * 设置能力积木列表
   * @param {Array} bricks - 能力积木数组
   */
  setBricks(bricks) {
    this.setState('bricks.list', bricks)
    this.setState('bricks.total', bricks.length)
    this.setState('bricks.lastUpdated', new Date().toISOString())
  }

  /**
   * 添加能力积木（云端同步）
   * @param {Object} brick - 能力积木对象
   */
  async addBrick(brick) {
    try {
      this.setState('bricks.loading', true)

      // 获取BrickManager实例
      const app = getApp()
      if (!app.brickManager) {
        throw new Error('BrickManager未初始化')
      }

      // 调用BrickManager添加积木
      const newBrick = await app.brickManager.addBrick(brick)

      // 状态已在BrickManager中更新，这里只需要更新loading状态
      this.setState('bricks.loading', false)
      this.setState('bricks.lastUpdated', new Date().toISOString())

      console.log('✅ 积木添加成功:', newBrick)
      return newBrick
    } catch (error) {
      this.setState('bricks.loading', false)
      console.error('❌ 添加积木失败:', error)
      throw error
    }
  }

  /**
   * 更新能力积木（云端同步）
   * @param {string|number} id - 积木ID
   * @param {Object} updates - 更新数据
   */
  async updateBrick(id, updates) {
    try {
      this.setState('bricks.loading', true)

      // 获取BrickManager实例
      const app = getApp()
      if (!app.brickManager) {
        throw new Error('BrickManager未初始化')
      }

      // 调用BrickManager更新积木
      await app.brickManager.updateBrick(id, updates)

      // 状态已在BrickManager中更新，这里只需要更新loading状态
      this.setState('bricks.loading', false)
      this.setState('bricks.lastUpdated', new Date().toISOString())

      console.log('✅ 积木更新成功')
      return true
    } catch (error) {
      this.setState('bricks.loading', false)
      console.error('❌ 更新积木失败:', error)
      throw error
    }
  }

  /**
   * 删除能力积木（云端同步）
   * @param {string|number} id - 积木ID
   */
  async deleteBrick(id) {
    try {
      this.setState('bricks.loading', true)

      // 获取BrickManager实例
      const app = getApp()
      if (!app.brickManager) {
        throw new Error('BrickManager未初始化')
      }

      // 调用BrickManager删除积木
      await app.brickManager.deleteBrick(id)

      // 状态已在BrickManager中更新，这里只需要更新loading状态
      this.setState('bricks.loading', false)
      this.setState('bricks.lastUpdated', new Date().toISOString())

      console.log('✅ 积木删除成功')
      return true
    } catch (error) {
      this.setState('bricks.loading', false)
      console.error('❌ 删除积木失败:', error)
      throw error
    }
  }

  /**
   * 刷新积木数据（从云端重新加载）
   */
  async refreshBricks() {
    try {
      this.setState('bricks.loading', true)

      // 获取BrickManager实例
      const app = getApp()
      if (!app.brickManager) {
        throw new Error('BrickManager未初始化')
      }

      // 从云端刷新数据
      const bricks = await app.brickManager.refreshFromCloud()

      this.setState('bricks.loading', false)
      this.setState('bricks.lastUpdated', new Date().toISOString())

      console.log('✅ 积木数据刷新成功')
      return bricks
    } catch (error) {
      this.setState('bricks.loading', false)
      console.error('❌ 刷新积木数据失败:', error)
      throw error
    }
  }

  // ===== 简历相关方法 =====

  /**
   * 设置简历列表
   * @param {Array} resumes - 简历数组
   */
  setResumes(resumes) {
    this.setState('resumes.list', resumes)
    this.setState('resumes.lastUpdated', new Date().toISOString())
  }

  /**
   * 添加简历
   * @param {Object} resume - 简历对象
   */
  addResume(resume) {
    const currentResumes = this.getState('resumes.list') || []
    const newResume = {
      ...resume,
      id: Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    const newResumes = [newResume, ...currentResumes]
    this.setResumes(newResumes)
    return newResume
  }

  /**
   * 设置当前简历
   * @param {Object} resume - 当前简历
   */
  setCurrentResume(resume) {
    this.setState('resumes.current', resume)
  }

  // ===== 生成相关方法 =====

  /**
   * 设置生成参数
   * @param {Object} params - 生成参数
   */
  setGenerationParams(params) {
    Object.keys(params).forEach(key => {
      this.setState(`generation.${key}`, params[key], false)
    })
  }

  /**
   * 开始生成
   */
  startGeneration() {
    this.setState('generation.isGenerating', true, false)
    this.setState('generation.progress', 0, false)
  }

  /**
   * 更新生成进度
   * @param {number} progress - 进度（0-100）
   */
  updateGenerationProgress(progress) {
    this.setState('generation.progress', progress, false)
  }

  /**
   * 结束生成
   */
  endGeneration() {
    this.setState('generation.isGenerating', false, false)
    this.setState('generation.progress', 100, false)
  }

  // ===== UI状态方法 =====

  /**
   * 设置活跃Tab
   * @param {number} index - Tab索引
   */
  setActiveTab(index) {
    this.setState('ui.activeTab', index, false)
  }

  /**
   * 设置页面加载状态
   * @param {boolean} loading - 加载状态
   */
  setPageLoading(loading) {
    this.setState('ui.pageLoading', loading, false)
  }

  /**
   * 设置网络状态
   * @param {string} status - 网络状态
   */
  setNetworkStatus(status) {
    this.setState('ui.networkStatus', status, false)
  }

  // ===== 工具方法 =====

  /**
   * 重置所有状态
   */
  reset() {
    this.logout()
    this.setBricks([])
    this.setResumes([])
    this.setState('generation', {
      company: '',
      position: '',
      jd: '',
      jdType: 'text',
      isGenerating: false,
      progress: 0
    }, false)
  }

  /**
   * 获取状态快照
   */
  getSnapshot() {
    return JSON.parse(JSON.stringify(this.state))
  }

  /**
   * 恢复状态快照
   * @param {Object} snapshot - 状态快照
   */
  restoreSnapshot(snapshot) {
    this.state = snapshot
    // 持久化所有状态
    Object.keys(snapshot).forEach(category => {
      this.persistState(category)
      this.syncToGlobalData(category)
    })
  }
}

// 创建全局状态管理器实例
const store = new Store()

// 导出状态管理器
module.exports = store 