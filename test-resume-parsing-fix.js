/**
 * 测试简历解析功能修复效果
 * 验证OCR和AI解析是否正常工作
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

async function testResumeParsingFix() {
  console.log('🧪 开始测试简历解析功能修复效果...');
  
  try {
    // 测试数据 - 使用一个简单的文本简历内容
    const testResumeContent = `
徐瑜泽
电话：13800138000
邮箱：<EMAIL>
地址：北京市朝阳区

教育背景：
2018-2022 北京大学 计算机科学与技术 本科

工作经历：
2022-2024 腾讯科技 前端工程师
- 负责微信小程序开发
- 参与云开发项目架构设计
- 优化用户体验，提升页面性能

技能：
- JavaScript, TypeScript
- React, Vue.js
- 微信小程序开发
- 云开发技术

项目经验：
AI简历系统 - 基于云开发的智能简历生成平台
`;

    console.log('📄 测试简历内容长度:', testResumeContent.length);

    // 直接调用resumeWorker云函数，传入简历内容
    const result = await cloud.callFunction({
      name: 'resumeWorker',
      data: {
        resumeContent: testResumeContent,
        fileName: '测试简历.txt',
        fileType: 'txt'
      }
    });

    console.log('✅ 云函数调用成功');
    console.log('📊 返回结果:', JSON.stringify(result.result, null, 2));

    // 验证解析结果
    const data = result.result.data;
    if (data && data.success) {
      const parseData = data.data;
      
      console.log('\n📋 解析结果验证:');
      console.log('个人信息:', parseData.personalInfo);
      console.log('教育背景数量:', parseData.education?.length || 0);
      console.log('工作经历数量:', parseData.workExperience?.length || 0);
      console.log('技能数量:', parseData.skills?.length || 0);
      console.log('项目数量:', parseData.projects?.length || 0);
      console.log('积木数量:', parseData.bricks?.length || 0);

      // 检查是否修复了数据提取问题
      const hasEducation = parseData.education && parseData.education.length > 0;
      const hasWorkExperience = parseData.workExperience && parseData.workExperience.length > 0;
      const hasPersonalInfo = parseData.personalInfo && parseData.personalInfo.name;

      console.log('\n🔍 修复效果评估:');
      console.log('✅ 个人信息提取:', hasPersonalInfo ? '成功' : '失败');
      console.log('✅ 教育背景提取:', hasEducation ? '成功' : '失败');
      console.log('✅ 工作经历提取:', hasWorkExperience ? '成功' : '失败');

      if (hasPersonalInfo && hasEducation && hasWorkExperience) {
        console.log('\n🎉 修复成功！所有关键数据都能正确提取');
        return true;
      } else {
        console.log('\n⚠️ 修复部分成功，仍有数据提取问题');
        return false;
      }
    } else {
      console.error('❌ 云函数返回失败结果:', data);
      return false;
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 执行测试
testResumeParsingFix().then(success => {
  if (success) {
    console.log('\n✅ 简历解析功能修复验证通过');
    process.exit(0);
  } else {
    console.log('\n❌ 简历解析功能修复验证失败');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ 测试执行失败:', error);
  process.exit(1);
});
