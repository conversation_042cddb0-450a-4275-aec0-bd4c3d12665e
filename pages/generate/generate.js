// pages/generate/generate.js
// 简历生成主页面 - 核心功能入口

const app = getApp()

Page({
  data: {
    // 用户输入数据
    companyName: '',
    positionName: '',
    jdContent: '',
    jdType: 'text', // 'text' | 'image'

    // 输入验证状态
    isCompanyValid: false,
    isPositionValid: false,
    isJdValid: false,

    // 能力积木状态
    bricksCount: 0,
    bricksLoaded: false,

    // 页面状态
    loading: false,
    isGenerating: false,

    // 用户状态
    userInfo: null,
    isLoggedIn: false,

    // 功能状态
    showImagePicker: false,
    imageUrl: '',

    // 快捷输入选项
    quickCompanies: ['腾讯', '阿里巴巴', '字节跳动', '美团', '百度', '京东'],
    quickPositions: ['前端工程师', '后端工程师', '产品经理', '数据分析师', '运营专员', 'UI设计师'],

    // 进度指示器数据
    progressSteps: [
      { id: 'input', name: '输入信息', status: 'current', icon: '📝' },
      { id: 'analyzing', name: '分析JD', status: 'waiting', icon: '🔍' },
      { id: 'generating', name: '生成简历', status: 'waiting', icon: '📄' },
      { id: 'preview', name: '预览简历', status: 'waiting', icon: '👁️' }
    ],
    currentStep: 'input',
    statusMessage: '请输入公司、职位和JD信息',
    showDetailedProgress: false,
    processingDetails: []
  },

  onLoad(options) {
    console.log('简历生成主页面加载')
    this.initializePage()
  },

  onShow() {
    console.log('简历生成主页面显示')
    this.checkLoginStatus()
    this.loadBricksCount()
  },

  // 初始化页面
  async initializePage() {
    try {
      // 检查登录状态
      await this.checkLoginStatus()

      // 加载能力积木数量
      await this.loadBricksCount()

      // 检查是否有传入的参数
      this.handlePageParams()

    } catch (error) {
      console.error('页面初始化失败:', error)
    }
  },

  // 检查登录状态 - 修复版本，避免循环跳转
  async checkLoginStatus() {
    try {
      console.log('🔍 [generate] 开始检查登录状态...');

      // 使用统一的登录状态管理器
      const LoginStateManager = global.LoginStateManager || getApp().LoginStateManager || require('../../utils/login-state-manager.js');
      const manager = new LoginStateManager();

      // 使用宽松的检查模式，避免误判
      const isLoggedIn = manager.checkLoginStatusSafely('pages/generate/generate');
      const loginInfo = manager.getLoginInfo();

      this.setData({
        userInfo: loginInfo?.userInfo || null,
        isLoggedIn: isLoggedIn
      });

      console.log(`🔍 [generate] 登录状态检查结果: ${isLoggedIn}`);

      // 只有在确实未登录时才跳转，并添加防护机制
      if (!isLoggedIn) {
        console.log('⚠️ [generate] 用户未登录，准备跳转到登录页面');

        // 添加延迟，避免时序问题
        setTimeout(() => {
          // 再次检查，避免时序问题导致的误判
          const recheckResult = manager.checkLoginStatusSafely('pages/generate/generate');
          console.log(`🔍 [generate] 延迟重检结果: ${recheckResult}`);

          if (!recheckResult) {
            console.log('🔄 [generate] 确认未登录，执行跳转');
            wx.navigateTo({
              url: '/pages/login/login?redirect=generate',
              fail: (err) => {
                console.error('❌ [generate] 跳转到登录页面失败:', err);
              }
            });
          } else {
            console.log('✅ [generate] 重检发现已登录，取消跳转');
            this.setData({
              userInfo: manager.getLoginInfo()?.userInfo || null,
              isLoggedIn: true
            });
          }
        }, 1000); // 1秒延迟
      } else {
        console.log('✅ [generate] 用户已登录，继续访问页面');
      }
    } catch (error) {
      console.error('❌ [generate] 检查登录状态失败:', error);
      // 错误情况下不跳转，避免循环
      console.log('🛡️ [generate] 登录检查出错，不执行跳转以避免循环');
    }
  },

  // 加载能力积木数量 - 使用统一的BrickManager
  async loadBricksCount() {
    try {
      console.log('🧱 开始加载能力积木数量...')

      // 使用统一的积木管理器实例
      const { instance: BrickManager } = require('../../utils/brick-manager.js')
      const bricks = await BrickManager.getBricks()

      console.log('✅ 通过BrickManager获取积木数据:', bricks.length)

      this.setData({
        bricksCount: bricks.length,
        bricksLoaded: true
      })

      console.log(`🧱 最终加载能力积木数量: ${bricks.length}`)

      // 如果仍然没有积木，给出详细的调试信息
      if (bricks.length === 0) {
        console.log('⚠️ 积木数量为0，调试信息:')
        console.log('- app.globalData.bricks:', app.globalData?.bricks?.length || 'undefined')
        console.log('- app.store bricks:', app.store?.getState('bricks.list')?.length || 'undefined')
        console.log('- 本地存储 bricks:', wx.getStorageSync('bricks')?.length || 'undefined')
        console.log('- 用户ID:', wx.getStorageSync('userId') || wx.getStorageSync('openid') || 'undefined')
      }

    } catch (error) {
      console.error('❌ 加载积木数量失败:', error)
      this.setData({
        bricksCount: 0,
        bricksLoaded: true
      })
    }
  },

  // 处理页面参数
  handlePageParams() {
    // 可以处理从其他页面传入的参数
    // 例如从积木库页面传入的预设数据
  },

  // 公司名称输入
  onCompanyInput(e) {
    const value = e.detail.value.trim()
    this.setData({
      companyName: value,
      isCompanyValid: value.length >= 2
    })
  },

  // 职位名称输入
  onPositionInput(e) {
    const value = e.detail.value.trim()
    this.setData({
      positionName: value,
      isPositionValid: value.length >= 2
    })
  },

  // JD内容输入
  onJdInput(e) {
    const value = e.detail.value.trim()
    this.setData({
      jdContent: value,
      isJdValid: value.length >= 50
    })
  },

  // 快捷选择公司
  selectQuickCompany(e) {
    const company = e.currentTarget.dataset.company
    this.setData({
      companyName: company,
      isCompanyValid: true
    })
  },

  // 快捷选择职位
  selectQuickPosition(e) {
    const position = e.currentTarget.dataset.position
    this.setData({
      positionName: position,
      isPositionValid: true
    })
  },

  // 切换JD输入类型
  switchJdType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      jdType: type,
      jdContent: type === 'image' ? '' : this.data.jdContent
    })
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        this.setData({
          imageUrl: tempFilePath,
          showImagePicker: false
        })

        // 开始图片识别
        this.recognizeImageText(tempFilePath)
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        })
      }
    })
  },

  // 图片文字识别 - 使用真实OCR服务
  async recognizeImageText(imagePath) {
    wx.showLoading({
      title: '识别中...',
      mask: true
    })

    try {
      console.log('📷 开始真实图片文字识别:', imagePath)

      // TODO: 暂时禁用OCR功能，等待HttpApiService修复
      console.log('⚠️ OCR功能暂时不可用，请手动输入JD内容')
      throw new Error('OCR功能暂时不可用，请手动输入JD内容')

      wx.hideLoading()

      if (ocrResult.success && ocrResult.data && ocrResult.data.text) {
        const recognizedText = ocrResult.data.text

        console.log('✅ OCR识别成功:', recognizedText.substring(0, 100) + '...')

        this.setData({
          jdContent: recognizedText,
          isJdValid: recognizedText.length >= 50
        })

        wx.showToast({
          title: '识别完成',
          icon: 'success'
        })
      } else {
        throw new Error('OCR识别失败或返回空内容')
      }

    } catch (error) {
      wx.hideLoading()
      console.error('❌ 图片识别失败:', error)

      // 提供更详细的错误信息
      let errorMessage = '识别失败，请重试'
      if (error.message.includes('上传')) {
        errorMessage = '图片上传失败，请检查网络'
      } else if (error.message.includes('OCR')) {
        errorMessage = '文字识别失败，请确保图片清晰'
      }

      wx.showToast({
        title: errorMessage,
        icon: 'error',
        duration: 3000
      })
    }
  },

  // 验证输入完整性
  validateInputs() {
    if (!this.data.isCompanyValid) {
      wx.showToast({
        title: '请输入公司名称',
        icon: 'none'
      })
      return false
    }

    if (!this.data.isPositionValid) {
      wx.showToast({
        title: '请输入职位名称',
        icon: 'none'
      })
      return false
    }

    if (!this.data.isJdValid) {
      wx.showToast({
        title: 'JD内容过短，请补充',
        icon: 'none'
      })
      return false
    }

    return true
  },

  // 开始生成简历
  async startGenerate() {
    if (!this.validateInputs()) {
      return
    }

    if (this.data.bricksCount === 0) {
      wx.showModal({
        title: '提示',
        content: '您还没有能力积木，是否先去上传简历提取积木？',
        confirmText: '去上传',
        cancelText: '继续',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/upload/upload'
            })
          } else {
            this.startJdAnalysisAndGeneration()
          }
        }
      })
      return
    }

    // 直接开始分析和生成流程
    this.startJdAnalysisAndGeneration()
  },

  // 开始JD分析和简历生成流程 - 使用统一智能架构
  async startJdAnalysisAndGeneration() {
    console.log('🚀 开始统一智能简历生成流程')

    // 显示进度指示器
    this.setData({
      isGenerating: true,
      showDetailedProgress: true,
      statusMessage: '开始智能简历生成...'
    })

    // 更新进度步骤
    this.updateProgressStep('analyzing')

    try {
      this.addProcessingDetail('开始统一智能简历生成')

      // 尝试使用统一架构生成
      const unifiedResult = await this.performUnifiedResumeGeneration()

      if (unifiedResult.success) {
        // 统一架构成功
        console.log('✅ 统一架构生成成功')

        // 更新到预览步骤
        this.updateProgressStep('preview')
        this.setData({ statusMessage: '准备预览简历...' })
        this.addProcessingDetail('统一架构生成完成，准备跳转预览')

        // 跳转到预览页面，传递统一生成的数据
        setTimeout(() => {
          this.goToPreviewUnified(unifiedResult.data)
        }, 1500)

      } else {
        // 统一架构失败，降级到经典流程
        console.log('⚠️ 统一架构失败，降级到经典流程')
        await this.startClassicGeneration()
      }

    } catch (error) {
      console.error('❌ 统一架构生成失败，尝试经典流程:', error)
      this.addProcessingDetail('统一架构失败，尝试经典模式')

      try {
        await this.startClassicGeneration()
      } catch (classicError) {
        console.error('❌ 经典流程也失败:', classicError)
        this.handleGenerationError(classicError)
      }
    }
  },

  // 经典分离式生成流程（备用方案）
  async startClassicGeneration() {
    console.log('🔄 开始经典分离式生成流程')

    try {
      this.addProcessingDetail('使用经典模式分析JD')

      // 真实JD分析过程
      const jdAnalysisResult = await this.performRealJdAnalysis()

      if (!jdAnalysisResult.success) {
        throw new Error('JD分析失败: ' + jdAnalysisResult.error)
      }

      // 更新到生成步骤
      this.updateProgressStep('generating')
      this.setData({ statusMessage: '正在生成简历...' })
      this.addProcessingDetail('开始生成简历')

      // 真实简历生成过程
      const resumeGenerationResult = await this.performRealResumeGeneration(jdAnalysisResult.data)

      if (!resumeGenerationResult.success) {
        throw new Error('简历生成失败: ' + resumeGenerationResult.error)
      }

      // 更新到预览步骤
      this.updateProgressStep('preview')
      this.setData({ statusMessage: '准备预览简历...' })
      this.addProcessingDetail('经典模式生成完成，准备跳转预览')

      // 跳转到预览页面，传递真实生成的数据
      setTimeout(() => {
        this.goToPreview(jdAnalysisResult.data, resumeGenerationResult.data)
      }, 1500)

    } catch (error) {
      console.error('❌ 生成简历失败:', error)
      this.addProcessingDetail('生成失败: ' + error.message)
      wx.showToast({
        title: '生成失败，请重试',
        icon: 'error'
      })
    } finally {
      this.setData({
        isGenerating: false
      })
    }
  },

  // 统一智能简历生成
  async performUnifiedResumeGeneration() {
    try {
      this.addProcessingDetail('准备统一智能生成')

      // 获取用户积木数据
      let userBricks = []
      if (app.globalData && app.globalData.bricks) {
        userBricks = app.globalData.bricks
      } else if (app.store) {
        userBricks = app.store.getState('bricks.list') || []
      } else {
        userBricks = wx.getStorageSync('bricks') || []
      }

      if (userBricks.length === 0) {
        throw new Error('未找到用户能力积木，请先上传简历')
      }

      console.log('🧱 使用用户积木数据:', userBricks.length)

      const generationData = {
        jdContent: this.data.jdContent,
        companyName: this.data.companyName,
        positionName: this.data.positionName,
        userBricks: userBricks,
        userInfo: this.data.userInfo,
        templateId: 'default'
      }

      console.log('📡 调用统一智能简历生成云函数:', generationData)
      this.addProcessingDetail('调用intelligentResumeGenerator统一函数')
      this.addProcessingDetail('AI分析JD内容')
      this.addProcessingDetail('智能匹配能力积木')

      // 直接调用云函数，不通过HttpApiService
      const cloudResult = await wx.cloud.callFunction({
        name: 'intelligentResumeGenerator',
        data: generationData
      })

      console.log('📡 云函数调用结果:', cloudResult)

      // 处理云函数返回结果
      let result;
      if (cloudResult.result && cloudResult.result.statusCode === 200) {
        const responseData = JSON.parse(cloudResult.result.body)
        result = responseData
      } else if (cloudResult.result) {
        result = cloudResult.result
      } else {
        throw new Error('云函数调用失败')
      }

      if (result.success) {
        this.addProcessingDetail('生成针对性简历')
        this.addProcessingDetail('优化简历结构')
        this.addProcessingDetail('统一架构生成完成')

        console.log('✅ 统一智能简历生成成功:', result.data)

        // 提取intelligentResumeGenerator返回的数据结构
        const extractedData = result.data?.data || result.data

        return {
          success: true,
          data: {
            resume: extractedData.resume,
            jdAnalysis: extractedData.jdAnalysis,
            matchedBricks: extractedData.matchedBricks,
            metadata: extractedData.metadata
          },
          architecture: 'unified'
        }
      } else {
        throw new Error(result.error || '统一智能简历生成失败')
      }

    } catch (error) {
      console.error('❌ 统一智能简历生成失败:', error)
      this.addProcessingDetail('统一生成失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // 跳转到预览页面 - 统一架构版本
  goToPreviewUnified(unifiedData) {
    const previewData = {
      type: 'unified', // 标记为统一架构生成的数据
      companyName: this.data.companyName,
      positionName: this.data.positionName,
      jdContent: this.data.jdContent,
      jdAnalysis: unifiedData.jdAnalysis, // JD分析结果
      resume: unifiedData.resume, // 简历生成结果
      matchedBricks: unifiedData.matchedBricks, // 匹配的积木
      metadata: unifiedData.metadata, // 元数据
      generatedAt: new Date().toISOString(),
      userInfo: this.data.userInfo,
      architecture: 'unified',
      processingTime: unifiedData.processingTime,
      matchingScore: unifiedData.metadata?.matchingScore || 0
    }

    console.log('🔄 跳转到预览页面（统一架构）:', previewData)

    wx.navigateTo({
      url: `/pages/preview/preview?data=${encodeURIComponent(JSON.stringify(previewData))}`
    })
  },

  // 更新进度步骤
  updateProgressStep(stepId) {
    const updatedSteps = this.data.progressSteps.map(step => {
      if (step.id === stepId) {
        return { ...step, status: 'current' }
      } else if (this.getStepIndex(step.id) < this.getStepIndex(stepId)) {
        return { ...step, status: 'done' }
      } else {
        return { ...step, status: 'waiting' }
      }
    })

    this.setData({
      progressSteps: updatedSteps,
      currentStep: stepId
    })
  },

  // 获取步骤索引
  getStepIndex(stepId) {
    const stepOrder = ['input', 'analyzing', 'generating', 'preview']
    return stepOrder.indexOf(stepId)
  },

  // 添加处理详情
  addProcessingDetail(message) {
    const now = new Date()
    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

    const newDetail = {
      time: timeStr,
      message: message
    }

    const updatedDetails = [...this.data.processingDetails, newDetail]

    // 只保留最近的5条记录
    if (updatedDetails.length > 5) {
      updatedDetails.shift()
    }

    this.setData({
      processingDetails: updatedDetails
    })
  },

  // 真实JD分析 - 调用intelligentResumeGenerator云函数
  async performRealJdAnalysis() {
    try {
      this.addProcessingDetail('解析JD关键词')

      const analysisData = {
        jdContent: this.data.jdContent,
        companyName: this.data.companyName,
        positionName: this.data.positionName,
        userBricks: [], // 空积木数组，仅进行JD分析
        personalInfo: {}, // 空个人信息
        templateId: 'default'
      }

      console.log('📡 调用intelligentResumeGenerator云函数进行JD分析:', analysisData)
      this.addProcessingDetail('调用intelligentResumeGenerator云函数（JD分析）')

      // 直接调用云函数，不通过HttpApiService
      const cloudResult = await wx.cloud.callFunction({
        name: 'intelligentResumeGenerator',
        data: analysisData
      })

      console.log('📡 云函数调用结果:', cloudResult)

      // 处理云函数返回结果
      let result;
      if (cloudResult.result && cloudResult.result.statusCode === 200) {
        const responseData = JSON.parse(cloudResult.result.body)
        result = responseData
      } else if (cloudResult.result) {
        result = cloudResult.result
      } else {
        throw new Error('云函数调用失败')
      }

      if (result.success) {
        this.addProcessingDetail('分析技能要求')
        this.addProcessingDetail('匹配能力积木')
        this.addProcessingDetail('JD分析完成')

        // 从intelligentResumeGenerator结果中提取JD分析数据
        const jdAnalysisData = result.data.jdAnalysis || result.data;
        console.log('✅ intelligentResumeGenerator JD分析成功:', jdAnalysisData)
        return {
          success: true,
          data: jdAnalysisData
        }
      } else {
        throw new Error(result.error || 'intelligentResumeGenerator调用失败')
      }

    } catch (error) {
      console.error('❌ intelligentResumeGenerator JD分析失败:', error)
      this.addProcessingDetail('JD分析失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // 真实简历生成 - 调用resumeWorker和cvGenerator SCF函数
  async performRealResumeGeneration(jdAnalysis) {
    try {
      this.addProcessingDetail('选择最佳积木组合')

      // 获取用户的能力积木数据
      let userBricks = []
      if (app.globalData && app.globalData.bricks) {
        userBricks = app.globalData.bricks
      } else if (app.store) {
        userBricks = app.store.getState('bricks.list') || []
      } else {
        userBricks = wx.getStorageSync('bricks') || []
      }

      console.log('🧱 使用用户积木数据:', userBricks.length)

      const generationData = {
        jdContent: this.data.jdContent,
        companyName: this.data.companyName,
        positionName: this.data.positionName,
        userBricks: userBricks,
        personalInfo: this.data.userInfo,
        templateId: 'default'
      }

      console.log('📡 调用intelligentResumeGenerator云函数进行简历生成:', generationData)
      this.addProcessingDetail('调用intelligentResumeGenerator云函数（简历生成）')

      // 直接调用云函数，不通过HttpApiService
      const cloudResult = await wx.cloud.callFunction({
        name: 'intelligentResumeGenerator',
        data: generationData
      })

      console.log('📡 云函数调用结果:', cloudResult)

      // 处理云函数返回结果
      let result;
      if (cloudResult.result && cloudResult.result.statusCode === 200) {
        const responseData = JSON.parse(cloudResult.result.body)
        result = responseData
      } else if (cloudResult.result) {
        result = cloudResult.result
      } else {
        throw new Error('云函数调用失败')
      }

      if (result.success) {
        this.addProcessingDetail('生成简历内容')

        // 从intelligentResumeGenerator结果中提取简历数据
        const resumeData = result.data.resume || result.data;

        if (resumeData.pdfUrl) {
          this.addProcessingDetail('生成PDF文件')
        }

        this.addProcessingDetail('优化简历结构')
        this.addProcessingDetail('简历生成完成')

        console.log('✅ intelligentResumeGenerator简历生成成功:', resumeData)
        return {
          success: true,
          data: resumeData
        }
      } else {
        throw new Error(result.error || 'intelligentResumeGenerator简历生成失败')
      }

    } catch (error) {
      console.error('❌ SCF函数简历生成失败:', error)
      this.addProcessingDetail('简历生成失败: ' + error.message)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 跳转到预览页面 - 传递真实生成数据
  goToPreview(jdAnalysis = null, resumeData = null) {
    const previewData = {
      type: 'generated', // 标记为AI生成的数据
      companyName: this.data.companyName,
      positionName: this.data.positionName,
      jdContent: this.data.jdContent,
      jdAnalysis: jdAnalysis, // 真实的JD分析结果
      resume: resumeData, // 真实的简历生成结果
      generatedAt: new Date().toISOString(),
      userInfo: this.data.userInfo,

      // 添加PDF相关信息
      pdfUrl: resumeData?.pdfUrl || null,
      fileID: resumeData?.fileID || null,
      cloudPath: resumeData?.cloudPath || null
    }

    console.log('📄 准备跳转到预览页面，携带真实数据:', previewData)

    const dataStr = encodeURIComponent(JSON.stringify(previewData))
    wx.navigateTo({
      url: `/pages/preview/preview?data=${dataStr}`,
      success: () => {
        console.log('✅ 跳转到预览页面成功')
        this.addProcessingDetail('成功跳转到预览页面')
      },
      fail: (err) => {
        console.error('❌ 跳转失败:', err)
        this.addProcessingDetail('跳转失败: ' + err.errMsg)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // 跳转到积木库 - 使用简化导航
  async goToBricks() {
    console.log('🧱 用户点击跳转到积木库');

    try {
      const SimpleNavigation = require('../../utils/simple-navigation.js');
      const result = await SimpleNavigation.navigateToBricks();

      if (!result.success && !result.skipped) {
        console.error('积木库导航失败:', result);
        wx.showToast({
          title: '页面跳转失败，请重试',
          icon: 'error',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('积木库导航异常:', error);
      wx.showToast({
        title: '页面跳转异常',
        icon: 'error',
        duration: 2000
      });
    }
  },

  // 跳转到设置 - 使用简化导航
  async goToSettings() {
    console.log('⚙️ 用户点击跳转到设置');

    try {
      const SimpleNavigation = require('../../utils/simple-navigation.js');
      const result = await SimpleNavigation.navigateToProfile();

      if (!result.success && !result.skipped) {
        console.error('设置页导航失败:', result);
        wx.showToast({
          title: '页面跳转失败，请重试',
          icon: 'error',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('设置页导航异常:', error);
      wx.showToast({
        title: '页面跳转异常',
        icon: 'error',
        duration: 2000
      });
    }
  },

  // 刷新积木数量
  refreshBricks() {
    this.loadBricksCount()
  }
})
