// 获取应用实例和服务
const app = getApp()

Page({
  data: {
    // 登录状态
    isLoading: false,
    isAgreed: true, // 默认同意协议

    // 手机号登录相关
    showPhoneModal: false,
    phoneNumber: '',
    verificationCode: '',
    codeSent: false,
    countdown: 60,

    // 调试模式
    debugMode: true, // 临时启用调试模式来解决超时问题

    // 统计数据
    userCount: 12580,
    resumeCount: 45620,
    successRate: 89,

    // 状态标记
    isRedirecting: false, // 跳转状态标记
    useMockData: false // 模拟数据模式标记
  },

  onLoad(options) {
    console.log('登录页面加载', options);

    // 保存页面参数，用于登录成功后的重定向
    this.setData({ pageOptions: options });

    // 解析页面参数
    this.parsePageParams(options);

    // 检查登录状态
    this.checkLoginStatus();

    // 检查是否使用模拟数据模式
    this.updateMockDataStatus();

    // 启动定时检查云开发连接状态
    this.startStatusCheck();
  },

  onReady() {
    console.log('登录页面准备完成')
  },

  onShow() {
    console.log('登录页面显示')
    // 只在非跳转情况下刷新状态
    if (!this.data.isRedirecting) {
      this.checkLoginStatus()
    }
  },

  onHide() {
    console.log('登录页面隐藏')
  },

  onUnload() {
    console.log('登录页面卸载')
  },

  parsePageParams(options) {
    // 解析页面参数，支持跳转参数等
    if (options && options.mock) {
      this.setData({ useMockData: options.mock === 'true' })
    }
  },

  updateMockDataStatus() {
    // 根据环境变量或参数设置模拟数据模式
    const useMock = false // 这里可以根据实际情况调整
    this.setData({ useMockData: useMock })
  },

  startStatusCheck() {
    // 禁用定时检查，避免自动跳转
    console.log('⚠️ 定时登录状态检查已禁用，避免自动跳转');
    // this.statusCheckInterval = setInterval(() => {
    //   this.checkLoginStatus()
    // }, 60000) // 每分钟检查一次
  },

  checkLoginStatus() {
    // 仅检查登录状态，不执行自动跳转
    console.log('🔍 检查登录状态（仅显示，不自动跳转）');

    try {
      const LoginStateManager = global.LoginStateManager || getApp().LoginStateManager || require('../../utils/login-state-manager.js');
      const manager = new LoginStateManager();

      // 获取当前页面路径
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const currentPagePath = currentPage ? currentPage.route : 'pages/login/login';

      console.log(`🔍 [${currentPagePath}] 当前页面`);

      // 只检查登录状态，不执行自动跳转
      const isLoggedIn = manager.isLoggedIn({
        silent: false,
        context: 'login-page-check'
      });

      if (isLoggedIn) {
        console.log('✅ 用户已登录（不执行自动跳转）');
        this.setData({
          isLoggedIn: true,
          isRedirecting: false
        });
      } else {
        console.log('⚠️ 用户未登录');
        this.setData({
          isLoggedIn: false,
          isRedirecting: false
        });
      }
    } catch (error) {
      console.error('❌ 登录状态检查失败:', error);
      this.setData({
        isLoggedIn: false,
        isRedirecting: false
      });
    }
  },

  // 备用登录状态检查（仅显示，不跳转）
  fallbackLoginStatusCheck() {
    console.log('🔄 执行备用登录状态检查（仅显示）');

    try {
      const userInfo = wx.getStorageSync('userInfo');
      const isLoggedIn = wx.getStorageSync('isLoggedIn');
      const sessionToken = wx.getStorageSync('session_token');

      if ((userInfo && userInfo.token) || (isLoggedIn && sessionToken)) {
        console.log('✅ 备用检查：检测到用户已登录（不自动跳转）');
        this.setData({
          isLoggedIn: true,
          isRedirecting: false
        });
      } else {
        console.log('⚠️ 备用检查：用户未登录');
        this.setData({
          isLoggedIn: false,
          isRedirecting: false
        });
      }
    } catch (error) {
      console.error('❌ 备用登录状态检查也失败:', error);
      this.setData({
        isLoggedIn: false,
        isRedirecting: false
      });
    }
  },

  onAgreeChange(e) {
    this.setData({ isAgreed: e.detail.value })
  },

  onPhoneInput(e) {
    this.setData({ phoneNumber: e.detail.value })
  },

  onCodeInput(e) {
    this.setData({ verificationCode: e.detail.value })
  },

  sendVerificationCode() {
    if (!this.data.phoneNumber) {
      wx.showToast({ title: '请输入手机号', icon: 'error' })
      return
    }
    if (this.data.codeSent) {
      return
    }
    this.setData({ codeSent: true, countdown: 60 })
    // 模拟发送验证码
    const timer = setInterval(() => {
      let countdown = this.data.countdown - 1
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({ codeSent: false })
      } else {
        this.setData({ countdown })
      }
    }, 1000)
    wx.showToast({ title: '验证码已发送', icon: 'success' })
  },

  phoneLogin() {
    if (!this.data.isAgreed) {
      wx.showToast({ title: '请先同意用户协议', icon: 'error' })
      return
    }
    if (!this.data.phoneNumber || !this.data.verificationCode) {
      wx.showToast({ title: '请输入手机号和验证码', icon: 'error' })
      return
    }
    this.setData({ isLoading: true })
    // 模拟登录请求
    setTimeout(() => {
      this.setData({ isLoading: false })
      wx.setStorageSync('userInfo', { token: 'mock-token', phone: this.data.phoneNumber })
      wx.showToast({ title: '登录成功', icon: 'success' })
      this.redirectAfterLogin()
    }, 1500)
  },

  async wechatLogin(e) {
    if (!this.data.isAgreed) {
      wx.showToast({ title: '请先同意用户协议', icon: 'error' })
      return
    }
    this.setData({ isLoading: true })

    try {
      console.log('🔧 使用增强版微信登录流程');

      // 使用增强版登录code验证器
      const LoginCodeValidator = require('../../utils/login-code-validator.js');
      const result = await LoginCodeValidator.enhancedWechatLogin();

      if (result.success) {
        console.log('✅ 增强版微信登录成功');
        this.handleLoginSuccess(result.data);
      } else {
        throw new Error(result.message || '登录失败');
      }

    } catch (error) {
      console.error('❌ 增强版微信登录失败:', error.message);

      // 如果增强版登录失败，回退到原始流程
      console.log('🔄 回退到原始登录流程...');
      this.fallbackWechatLogin();
    }
  },

  // 原始微信登录流程（作为备用）
  fallbackWechatLogin() {
    console.log('📱 执行原始微信登录流程');

    wx.login({
      success: (res) => {
        console.log('wx.login result:', res);
        if (res.code) {
          console.log('✅ 获取到code:', res.code.substring(0, 8) + '...');
          this.getUserProfile(res.code)
        } else {
          console.error('❌ wx.login未返回code');
          wx.showToast({ title: '登录失败，请重试', icon: 'error' })
          this.setData({ isLoading: false })
        }
      },
      fail: (err) => {
        console.error('❌ wx.login调用失败:', err);
        wx.showToast({ title: '登录失败，请检查网络', icon: 'error' })
        this.setData({ isLoading: false })
      }
    })
  },

  // 处理登录成功
  async handleLoginSuccess(loginData) {
    console.log('🎉 处理登录成功:', loginData);

    try {
      // 1. 使用统一的登录状态管理器
      const LoginStateManager = global.LoginStateManager || getApp().LoginStateManager || require('../../utils/login-state-manager.js');

      // 创建管理器实例
      const manager = new LoginStateManager();
      const success = manager.setLoginState(loginData);

      if (!success) {
        console.warn('⚠️ 登录状态管理器设置失败，使用备用方案');
        // 备用方案：直接存储关键信息
        this.fallbackLoginStateStorage(loginData);
      } else {
        console.log('✅ 登录状态管理完成');
      }

      // 2. 建立微信云开发身份验证
      console.log('🔐 建立微信云开发身份验证...');
      const cloudAuthSuccess = await this.establishCloudAuth();
      if (cloudAuthSuccess) {
        console.log('✅ 微信云开发身份验证建立成功');
      } else {
        console.warn('⚠️ 微信云开发身份验证建立失败，但不影响登录流程');
      }

    } catch (error) {
      console.error('❌ 登录状态管理失败:', error);
      console.log('🔄 使用备用登录状态存储方案');

      // 备用方案
      this.fallbackLoginStateStorage(loginData);
    }

    this.setData({ isLoading: false });
    wx.showToast({ title: '登录成功', icon: 'success' });

    // 优化延迟跳转 - 确保登录状态完全设置后再跳转
    console.log('🎉 [login] 登录成功，准备跳转...');
    setTimeout(() => {
      // 验证登录状态是否正确设置
      try {
        const LoginStateManager = global.LoginStateManager || getApp().LoginStateManager || require('../../utils/login-state-manager.js');
        const manager = new LoginStateManager();

        if (manager.verifyLoginState()) {
          console.log('✅ [login] 登录状态验证通过，执行跳转');
          this.redirectAfterLogin();
        } else {
          console.warn('⚠️ [login] 登录状态验证失败，延长等待时间');
          // 再等待1秒后重试
          setTimeout(() => {
            console.log('🔄 [login] 重试跳转');
            this.redirectAfterLogin();
          }, 1000);
        }
      } catch (error) {
        console.error('❌ [login] 登录状态验证出错，直接跳转:', error);
        this.redirectAfterLogin();
      }
    }, 2000); // 增加到2秒延迟
  },

  // 备用登录状态存储方案
  fallbackLoginStateStorage(loginData) {
    console.log('🔄 执行备用登录状态存储');

    try {
      // 直接存储关键信息
      if (loginData.session_token) {
        wx.setStorageSync('session_token', loginData.session_token);
      }
      if (loginData.user_id) {
        wx.setStorageSync('user_id', loginData.user_id);
      }
      if (loginData.openid) {
        wx.setStorageSync('openid', loginData.openid);
      }
      if (loginData.user_info) {
        wx.setStorageSync('user_info', loginData.user_info);
      }

      // 设置登录标志
      wx.setStorageSync('isLoggedIn', true);
      wx.setStorageSync('loginTime', Date.now());

      // 更新全局状态
      if (getApp().globalData) {
        getApp().globalData.isLoggedIn = true;
        getApp().globalData.userInfo = loginData.user_info;
        getApp().globalData.sessionToken = loginData.session_token;
      }

      console.log('✅ 备用登录状态存储完成');

    } catch (error) {
      console.error('❌ 备用登录状态存储也失败:', error);
      wx.showToast({
        title: '登录状态保存失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 建立微信云开发身份验证
  async establishCloudAuth() {
    try {
      console.log('🔐 建立微信云开发身份验证...');

      // 尝试调用ping云函数来建立身份
      const result = await wx.cloud.callFunction({
        name: 'ping',
        data: { action: 'establish-auth' }
      });

      if (result && result.result && result.result.success) {
        console.log('✅ 微信云开发身份建立成功，OPENID:', result.result.openid);

        // 将OPENID存储到本地，供后续使用
        if (result.result.openid) {
          wx.setStorageSync('cloud_openid', result.result.openid);

          // 更新全局状态
          const app = getApp();
          if (app.globalData) {
            app.globalData.cloudOpenid = result.result.openid;
            app.globalData.isCloudConnected = true;
          }
        }

        return true;
      } else {
        console.warn('⚠️ 微信云开发身份建立失败，结果:', result);
        return false;
      }

    } catch (error) {
      console.error('❌ 建立微信云开发身份失败:', error);
      return false;
    }
  },

  getUserProfile(code) {
    // 获取用户信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res.userInfo)
        this.exchangeCodeForToken(code, res.userInfo)
      },
      fail: () => {
        // 即使用户拒绝授权，也可以使用 code 进行登录
        console.log('用户拒绝授权，使用基础登录')
        this.exchangeCodeForToken(code, null)
      }
    })
  },

  async exchangeCodeForToken(code, userInfo) {
    try {
      console.log('开始后台登录验证', {
        code: code ? code.substring(0, 8) + '...' : 'null',
        userInfo: userInfo,
        timestamp: new Date().toISOString()
      })

      // 引入增强版登录助手
      const LoginHelper = require('../../utils/login-helper.js')

      console.log('使用增强版登录助手')
      const startTime = Date.now()

      // 使用增强版登录方法，自动处理超时和重试
      const response = await LoginHelper.enhancedLogin(code, userInfo)

      const endTime = Date.now()
      const duration = endTime - startTime
      console.log('增强版登录完成', {
        duration: duration + 'ms',
        success: response?.success,
        isFallback: response?.data?.isFallback
      })

      if (response.success) {
        // 使用统一的登录成功处理函数
        this.handleLoginSuccess({
          session_token: response.data.sessionToken,
          user_id: response.data.userId,
          openid: response.data.openid,
          user_info: response.data.userInfo,
          expires_in: response.data.expiresIn
        });
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)

      // 提供更友好的中文错误提示
      let errorMessage = '登录失败，请重试';

      if (error.message) {
        if (error.message.includes('超时') || error.message.includes('timeout')) {
          errorMessage = '登录请求超时，请检查网络连接后重试';
          console.log('🚨 登录超时错误详情:', {
            message: error.message,
            timestamp: new Date().toISOString(),
            suggestion: '请检查网络连接或稍后重试'
          });
        } else if (error.message.includes('网络连接失败') || error.message.includes('fail')) {
          errorMessage = '网络连接失败，请检查网络后重试';
        } else if (error.message.includes('登录已过期')) {
          errorMessage = '登录信息已过期，请重新登录';
        } else if (error.message.includes('服务不可用')) {
          errorMessage = '登录服务暂时不可用，请稍后重试';
        } else if (error.message.includes('离线模式')) {
          errorMessage = '当前为离线模式，部分功能可能受限';
        } else {
          errorMessage = error.message;
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
      this.setData({ isLoading: false })
    }
  },

  redirectAfterLogin() {
    console.log('🔄 登录成功后重定向到简历生成页面');
    this.setData({ isRedirecting: true });

    // 使用导航管理器进行正确的页面跳转
    try {
      const NavigationManager = require('../../utils/navigation.js');
      const navManager = new NavigationManager();

      // 检查是否有重定向参数
      const options = this.data.pageOptions || {};
      if (options.redirectTo) {
        console.log('发现重定向参数，跳转到:', options.redirectTo);
        navManager.handleLoginSuccess({
          redirectTo: options.redirectTo,
          redirectParams: options.redirectParams
        });
      } else {
        console.log('无重定向参数，跳转到主页面（简历生成页面）');
        navManager.navigateToHome({ replace: true });
      }
    } catch (error) {
      console.error('❌ 使用导航管理器跳转失败，使用备用方案:', error);
      // 备用方案：直接跳转到简历生成页面
      wx.switchTab({
        url: '/pages/generate/generate',
        success: () => {
          console.log('✅ 成功跳转到简历生成页面');
        },
        fail: (err) => {
          console.error('❌ 跳转失败:', err);
          // 最后的备用方案：使用redirectTo
          wx.redirectTo({
            url: '/pages/generate/generate'
          });
        }
      });
    }
  },

  openPhoneModal() {
    this.setData({ showPhoneModal: true })
  },

  closePhoneModal() {
    this.setData({ showPhoneModal: false, phoneNumber: '', verificationCode: '', codeSent: false, countdown: 60 })
  },

  // 网络诊断
  async networkDiagnosis() {
    wx.showLoading({ title: '诊断网络...' });

    try {
      const LoginHelper = require('../../utils/login-helper.js');
      const results = await LoginHelper.networkDiagnosis();

      wx.hideLoading();

      let message = `网络类型: ${results.networkType}\n\n`;
      results.tests.forEach(test => {
        message += `${test.name}: ${test.status === 'success' ? '✅' : '❌'}\n`;
        if (test.duration) {
          message += `耗时: ${test.duration}ms\n`;
        }
        if (test.error) {
          message += `错误: ${test.error}\n`;
        }
        message += '\n';
      });

      wx.showModal({
        title: '网络诊断结果',
        content: message,
        showCancel: false
      });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '诊断失败: ' + error.message,
        icon: 'none'
      });
    }
  },

  // 跳转到调试页面
  goToDebugPage() {
    wx.navigateTo({
      url: '/pages/debug/debug'
    });
  },

  // 诊断code参数问题
  async diagnoseCodeIssue() {
    wx.showLoading({ title: '诊断中...' });

    try {
      const LoginCodeValidator = require('../../utils/login-code-validator.js');
      const diagnostics = await LoginCodeValidator.diagnoseLoginIssue();

      wx.hideLoading();

      // 生成诊断报告
      let report = '登录诊断报告:\n\n';
      const items = [
        { key: 'wxLoginAvailable', name: 'wx.login API' },
        { key: 'codeGeneration', name: 'code生成' },
        { key: 'userInfoAccess', name: '用户信息获取' },
        { key: 'networkConnectivity', name: '网络连通性' },
        { key: 'scfFunctionAccess', name: 'SCF函数访问' }
      ];

      items.forEach(item => {
        const status = diagnostics[item.key] ? '✅' : '❌';
        report += `${status} ${item.name}\n`;
      });

      // 添加建议
      report += '\n建议:\n';
      if (!diagnostics.codeGeneration) {
        report += '• 检查微信开发者工具设置\n';
        report += '• 确认小程序AppID配置正确\n';
      }
      if (!diagnostics.networkConnectivity) {
        report += '• 检查网络连接\n';
        report += '• 确认域名配置正确\n';
      }
      if (!diagnostics.scfFunctionAccess) {
        report += '• 检查SCF函数状态\n';
        report += '• 验证函数URL是否正确\n';
      }

      wx.showModal({
        title: 'Code参数诊断',
        content: report,
        showCancel: false,
        confirmText: '知道了'
      });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '诊断失败: ' + error.message,
        icon: 'none',
        duration: 3000
      });
    }
  },

  onUnload() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval)
    }
  }
})