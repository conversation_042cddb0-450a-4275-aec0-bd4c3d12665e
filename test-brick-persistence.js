/**
 * 积木数据持久化测试脚本
 * 用于验证积木数据的云端存储和同步功能
 */

// 测试数据
const testBricks = [
  {
    id: 'test_brick_1',
    title: '前端开发技能',
    content: '熟练掌握Vue.js、React、微信小程序开发，具备3年以上前端开发经验',
    category: '技术能力',
    level: '熟练',
    tags: ['Vue.js', 'React', '微信小程序'],
    confidence: 0.9,
    usageCount: 5
  },
  {
    id: 'test_brick_2',
    title: '项目管理经验',
    content: '负责过多个中大型项目的管理工作，具备敏捷开发和团队协作经验',
    category: '管理能力',
    level: '精通',
    tags: ['项目管理', '敏捷开发', '团队协作'],
    confidence: 0.85,
    usageCount: 3
  },
  {
    id: 'test_brick_3',
    title: '数据分析能力',
    content: '熟练使用Python进行数据分析，掌握机器学习基础算法',
    category: '技术能力',
    level: '熟练',
    tags: ['Python', '数据分析', '机器学习'],
    confidence: 0.8,
    usageCount: 2
  }
];

/**
 * 测试积木添加功能
 */
async function testAddBricks() {
  console.log('🧪 开始测试积木添加功能...');
  
  try {
    const app = getApp();
    if (!app.brickManager) {
      throw new Error('BrickManager未初始化');
    }

    const results = [];
    for (const testBrick of testBricks) {
      try {
        const result = await app.brickManager.addBrick(testBrick);
        results.push({ success: true, brick: result });
        console.log(`✅ 积木 "${testBrick.title}" 添加成功`);
      } catch (error) {
        results.push({ success: false, error: error.message, brick: testBrick });
        console.error(`❌ 积木 "${testBrick.title}" 添加失败:`, error.message);
      }
    }

    return results;
  } catch (error) {
    console.error('❌ 测试积木添加功能失败:', error);
    throw error;
  }
}

/**
 * 测试积木查询功能
 */
async function testListBricks() {
  console.log('🧪 开始测试积木查询功能...');
  
  try {
    const app = getApp();
    if (!app.brickManager) {
      throw new Error('BrickManager未初始化');
    }

    const bricks = await app.brickManager.loadFromCloudDatabase();
    console.log(`✅ 查询到 ${bricks.length} 个积木`);
    
    return {
      success: true,
      count: bricks.length,
      bricks: bricks
    };
  } catch (error) {
    console.error('❌ 测试积木查询功能失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试积木更新功能
 */
async function testUpdateBrick() {
  console.log('🧪 开始测试积木更新功能...');
  
  try {
    const app = getApp();
    if (!app.brickManager) {
      throw new Error('BrickManager未初始化');
    }

    // 更新第一个测试积木
    const updateData = {
      title: '前端开发技能（已更新）',
      content: '熟练掌握Vue.js、React、微信小程序开发，具备5年以上前端开发经验',
      usageCount: 10
    };

    await app.brickManager.updateBrick('test_brick_1', updateData);
    console.log('✅ 积木更新成功');
    
    return { success: true };
  } catch (error) {
    console.error('❌ 测试积木更新功能失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试积木删除功能
 */
async function testDeleteBrick() {
  console.log('🧪 开始测试积木删除功能...');
  
  try {
    const app = getApp();
    if (!app.brickManager) {
      throw new Error('BrickManager未初始化');
    }

    await app.brickManager.deleteBrick('test_brick_3');
    console.log('✅ 积木删除成功');
    
    return { success: true };
  } catch (error) {
    console.error('❌ 测试积木删除功能失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试数据同步功能
 */
async function testSyncBricks() {
  console.log('🧪 开始测试数据同步功能...');
  
  try {
    const app = getApp();
    if (!app.brickManager) {
      throw new Error('BrickManager未初始化');
    }

    const syncResult = await app.brickManager.syncToCloud();
    console.log('✅ 数据同步成功:', syncResult);
    
    return { success: true, result: syncResult };
  } catch (error) {
    console.error('❌ 测试数据同步功能失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 性能测试
 */
async function testPerformance() {
  console.log('🧪 开始性能测试...');
  
  const results = {
    addTime: 0,
    listTime: 0,
    updateTime: 0,
    deleteTime: 0,
    syncTime: 0
  };

  try {
    // 测试添加性能
    const addStart = Date.now();
    await testAddBricks();
    results.addTime = Date.now() - addStart;

    // 测试查询性能
    const listStart = Date.now();
    await testListBricks();
    results.listTime = Date.now() - listStart;

    // 测试更新性能
    const updateStart = Date.now();
    await testUpdateBrick();
    results.updateTime = Date.now() - updateStart;

    // 测试同步性能
    const syncStart = Date.now();
    await testSyncBricks();
    results.syncTime = Date.now() - syncStart;

    // 测试删除性能
    const deleteStart = Date.now();
    await testDeleteBrick();
    results.deleteTime = Date.now() - deleteStart;

    console.log('📊 性能测试结果:', results);
    
    // 验证性能指标
    const performanceCheck = {
      addTime: results.addTime <= 3000, // 3秒内
      listTime: results.listTime <= 3000, // 3秒内
      updateTime: results.updateTime <= 3000, // 3秒内
      deleteTime: results.deleteTime <= 3000, // 3秒内
      syncTime: results.syncTime <= 5000 // 5秒内
    };

    const allPassed = Object.values(performanceCheck).every(passed => passed);
    
    return {
      success: allPassed,
      results: results,
      checks: performanceCheck
    };
  } catch (error) {
    console.error('❌ 性能测试失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 运行完整测试套件
 */
async function runFullTest() {
  console.log('🚀 开始运行积木数据持久化完整测试...');
  
  const testResults = {
    timestamp: new Date().toISOString(),
    tests: {}
  };

  try {
    // 1. 测试添加功能
    testResults.tests.add = await testAddBricks();
    
    // 2. 测试查询功能
    testResults.tests.list = await testListBricks();
    
    // 3. 测试更新功能
    testResults.tests.update = await testUpdateBrick();
    
    // 4. 测试删除功能
    testResults.tests.delete = await testDeleteBrick();
    
    // 5. 测试同步功能
    testResults.tests.sync = await testSyncBricks();
    
    // 6. 性能测试
    testResults.tests.performance = await testPerformance();

    // 计算总体成功率
    const successCount = Object.values(testResults.tests).filter(test => 
      Array.isArray(test) ? test.some(t => t.success) : test.success
    ).length;
    
    testResults.successRate = (successCount / Object.keys(testResults.tests).length) * 100;
    testResults.overall = testResults.successRate >= 95 ? 'PASS' : 'FAIL';

    console.log('📋 测试完成，总体结果:', testResults.overall);
    console.log('📊 成功率:', testResults.successRate + '%');
    
    return testResults;
  } catch (error) {
    console.error('❌ 完整测试运行失败:', error);
    testResults.error = error.message;
    testResults.overall = 'ERROR';
    return testResults;
  }
}

// 导出测试函数
module.exports = {
  testAddBricks,
  testListBricks,
  testUpdateBrick,
  testDeleteBrick,
  testSyncBricks,
  testPerformance,
  runFullTest
};
