#!/usr/bin/env node

/**
 * 部署验证脚本
 * 用于验证云托管服务是否正常运行
 */

const https = require('https');
const http = require('http');

class DeploymentVerifier {
  constructor(options = {}) {
    this.timeout = options.timeout || 30000;
    this.retries = options.retries || 3;
    this.retryDelay = options.retryDelay || 5000;
  }

  /**
   * 发送 HTTP 请求
   */
  async makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const isHttps = url.startsWith('https://');
      const client = isHttps ? https : http;
      
      const req = client.request(url, {
        method: options.method || 'GET',
        headers: options.headers || {},
        timeout: this.timeout
      }, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        });
      });
      
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
      
      if (options.body) {
        req.write(options.body);
      }
      
      req.end();
    });
  }

  /**
   * 带重试的请求
   */
  async requestWithRetry(url, options = {}) {
    let lastError;
    
    for (let i = 0; i < this.retries; i++) {
      try {
        console.log(`🔄 尝试请求 ${url} (${i + 1}/${this.retries})`);
        const response = await this.makeRequest(url, options);
        return response;
      } catch (error) {
        lastError = error;
        console.log(`❌ 请求失败: ${error.message}`);
        
        if (i < this.retries - 1) {
          console.log(`⏳ ${this.retryDelay / 1000}秒后重试...`);
          await this.sleep(this.retryDelay);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 延迟函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证健康检查端点
   */
  async verifyHealth(baseUrl) {
    console.log('\n🏥 验证健康检查端点...');
    
    try {
      const response = await this.requestWithRetry(`${baseUrl}/health`);
      
      if (response.statusCode === 200) {
        const data = JSON.parse(response.body);
        console.log('✅ 健康检查通过');
        console.log(`   状态: ${data.status}`);
        console.log(`   服务: ${data.service}`);
        console.log(`   时间: ${data.timestamp}`);
        return true;
      } else {
        console.log(`❌ 健康检查失败: HTTP ${response.statusCode}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ 健康检查异常: ${error.message}`);
      return false;
    }
  }

  /**
   * 验证根端点
   */
  async verifyRoot(baseUrl) {
    console.log('\n🏠 验证根端点...');
    
    try {
      const response = await this.requestWithRetry(baseUrl);
      
      if (response.statusCode === 200) {
        const data = JSON.parse(response.body);
        console.log('✅ 根端点正常');
        console.log(`   消息: ${data.message}`);
        console.log(`   版本: ${data.version}`);
        return true;
      } else {
        console.log(`❌ 根端点失败: HTTP ${response.statusCode}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ 根端点异常: ${error.message}`);
      return false;
    }
  }

  /**
   * 验证截图功能
   */
  async verifySnapshot(baseUrl) {
    console.log('\n📸 验证截图功能...');
    
    const testHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>测试简历</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          h1 { color: #333; }
        </style>
      </head>
      <body>
        <h1>测试简历</h1>
        <p>这是一个测试用的简历内容。</p>
        <p>生成时间: ${new Date().toISOString()}</p>
      </body>
      </html>
    `;
    
    try {
      const response = await this.requestWithRetry(`${baseUrl}/resume-snapshot`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          html: testHtml,
          options: {
            width: 800,
            height: 600,
            format: 'png'
          }
        })
      });
      
      if (response.statusCode === 200) {
        const contentType = response.headers['content-type'];
        if (contentType && contentType.includes('image/png')) {
          console.log('✅ 截图功能正常');
          console.log(`   响应大小: ${response.body.length} bytes`);
          console.log(`   内容类型: ${contentType}`);
          return true;
        } else {
          console.log(`❌ 截图功能异常: 错误的内容类型 ${contentType}`);
          return false;
        }
      } else {
        console.log(`❌ 截图功能失败: HTTP ${response.statusCode}`);
        console.log(`   响应: ${response.body}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ 截图功能异常: ${error.message}`);
      return false;
    }
  }

  /**
   * 运行完整验证
   */
  async verify(baseUrl) {
    console.log(`🚀 开始验证部署: ${baseUrl}`);
    console.log(`⚙️  配置: 超时=${this.timeout}ms, 重试=${this.retries}次`);
    
    const results = {
      health: false,
      root: false,
      snapshot: false
    };
    
    // 验证各个端点
    results.health = await this.verifyHealth(baseUrl);
    results.root = await this.verifyRoot(baseUrl);
    results.snapshot = await this.verifySnapshot(baseUrl);
    
    // 输出总结
    console.log('\n📊 验证结果总结:');
    console.log(`   健康检查: ${results.health ? '✅' : '❌'}`);
    console.log(`   根端点: ${results.root ? '✅' : '❌'}`);
    console.log(`   截图功能: ${results.snapshot ? '✅' : '❌'}`);
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      console.log('\n🎉 所有验证通过！部署成功！');
      process.exit(0);
    } else {
      console.log('\n💥 部分验证失败，请检查服务状态');
      process.exit(1);
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const baseUrl = args[0];
  
  if (!baseUrl) {
    console.log('❌ 请提供服务 URL');
    console.log('用法: node verify-deployment.js <SERVICE_URL>');
    console.log('示例: node verify-deployment.js https://your-service.example.com');
    process.exit(1);
  }
  
  const verifier = new DeploymentVerifier({
    timeout: 30000,
    retries: 3,
    retryDelay: 5000
  });
  
  try {
    await verifier.verify(baseUrl);
  } catch (error) {
    console.log(`💥 验证过程出错: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = DeploymentVerifier;
