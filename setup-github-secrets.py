#!/usr/bin/env python3
"""
设置GitHub仓库的Secrets
"""

import requests
import base64
import json
from nacl import encoding, public

def encrypt_secret(public_key: str, secret_value: str) -> str:
    """使用仓库公钥加密secret"""
    public_key_bytes = base64.b64decode(public_key)
    public_key_obj = public.PublicKey(public_key_bytes)
    box = public.SealedBox(public_key_obj)
    encrypted = box.encrypt(secret_value.encode("utf-8"))
    return base64.b64encode(encrypted).decode("utf-8")

def setup_secrets():
    # GitHub配置
    GITHUB_TOKEN = "****************************************"
    REPO_OWNER = "xuyuzeamazon"
    REPO_NAME = "ai-resume-cloudrun"
    
    # 要设置的secrets
    secrets = {
        "CLOUDBASE_SECRET_ID": "AKIDhNq56uVs30Ys0ub4JK8j6FI8k8IMDqFR",
        "CLOUDBASE_SECRET_KEY": "1oqjRwEJCtNfliU2NVQ7dBgzJUYQAF9q",
        "CLOUDBASE_ENV_ID": "zemuresume-4gjvx1wea78e3d1e"
    }
    
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    # 获取仓库公钥
    print("🔑 获取仓库公钥...")
    public_key_url = f"https://api.github.com/repos/{REPO_OWNER}/{REPO_NAME}/actions/secrets/public-key"
    response = requests.get(public_key_url, headers=headers)
    
    if response.status_code != 200:
        print(f"❌ 获取公钥失败: {response.status_code}")
        print(response.text)
        return
    
    public_key_data = response.json()
    public_key = public_key_data["key"]
    key_id = public_key_data["key_id"]
    
    print(f"✅ 公钥获取成功，Key ID: {key_id}")
    
    # 设置每个secret
    for secret_name, secret_value in secrets.items():
        print(f"🔐 设置 {secret_name}...")
        
        # 加密secret值
        encrypted_value = encrypt_secret(public_key, secret_value)
        
        # 设置secret
        secret_url = f"https://api.github.com/repos/{REPO_OWNER}/{REPO_NAME}/actions/secrets/{secret_name}"
        secret_data = {
            "encrypted_value": encrypted_value,
            "key_id": key_id
        }
        
        response = requests.put(secret_url, headers=headers, json=secret_data)
        
        if response.status_code in [201, 204]:
            print(f"✅ {secret_name} 设置成功")
        else:
            print(f"❌ {secret_name} 设置失败: {response.status_code}")
            print(response.text)
    
    print("\n🎉 GitHub Secrets设置完成！")
    print("\n📋 已设置的Secrets:")
    for secret_name in secrets.keys():
        print(f"  - {secret_name}")
    
    print(f"\n🔗 GitHub仓库: https://github.com/{REPO_OWNER}/{REPO_NAME}")
    print(f"🔗 GitHub Actions: https://github.com/{REPO_OWNER}/{REPO_NAME}/actions")
    print(f"🔗 Settings: https://github.com/{REPO_OWNER}/{REPO_NAME}/settings/secrets/actions")

if __name__ == "__main__":
    try:
        setup_secrets()
    except ImportError:
        print("❌ 需要安装PyNaCl库:")
        print("pip install PyNaCl")
    except Exception as e:
        print(f"❌ 设置失败: {e}")
