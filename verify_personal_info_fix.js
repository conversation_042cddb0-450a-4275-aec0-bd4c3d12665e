/**
 * 验证个人信息积木修复效果
 * 在微信开发者工具Console中运行
 */

function verifyPersonalInfoFix() {
  console.log('🔧 开始验证个人信息积木修复效果...');
  
  try {
    // 1. 检查当前页面
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route !== 'pages/bricks/bricks') {
      console.log('⚠️ 请先导航到积木库页面 (pages/bricks/bricks)');
      return false;
    }
    
    // 2. 检查页面数据
    const bricks = currentPage.data.bricks || [];
    const personalCount = currentPage.data.personalCount || 0;
    
    console.log('📊 当前页面数据:');
    console.log('- 总积木数:', bricks.length);
    console.log('- 个人信息积木数:', personalCount);
    
    // 3. 查找个人信息积木
    const personalBricks = bricks.filter(brick => 
      brick.category === 'personal'
    );
    
    console.log('👤 个人信息积木详情:');
    if (personalBricks.length > 0) {
      personalBricks.forEach((brick, index) => {
        console.log(`积木 ${index + 1}:`, {
          id: brick.id,
          title: brick.title,
          description: brick.description.substring(0, 50) + '...',
          category: brick.category,
          source: brick.source,
          isDefault: brick.isDefault,
          enhancedByAI: brick.enhancedByAI
        });
        
        // 检查是否是默认积木或包含占位文本
        if (brick.isDefault || 
            brick.description.includes('请上传简历') || 
            brick.description.includes('暂无积木数据') ||
            (brick.title === '个人信息' && !brick.enhancedByAI)) {
          console.log('❌ 发现默认/占位积木，这可能是多余显示的原因');
          console.log('问题积木:', brick);
          return false;
        }
      });
      
      console.log('✅ 个人信息积木显示正常，来自真实AI解析');
      return true;
    } else {
      console.log('✅ 未找到个人信息积木（符合预期，避免显示占位内容）');
      return true;
    }
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
    return false;
  }
}

// 检查BrickManager默认积木配置
function checkBrickManagerConfig() {
  console.log('🔍 检查BrickManager默认积木配置...');
  
  try {
    const BrickManager = require('utils/brick-manager.js');
    const defaultBricks = BrickManager.getDefaultBricks();
    
    console.log('📦 默认积木数量:', defaultBricks.length);
    
    if (defaultBricks.length === 0) {
      console.log('✅ 默认积木配置正确：返回空数组');
      return true;
    } else {
      console.log('❌ 默认积木配置有问题：仍在生成默认积木');
      defaultBricks.forEach((brick, index) => {
        console.log(`默认积木 ${index + 1}:`, {
          id: brick.id,
          title: brick.title,
          category: brick.category,
          isDefault: brick.isDefault
        });
      });
      return false;
    }
  } catch (error) {
    console.error('❌ 检查BrickManager配置失败:', error);
    return false;
  }
}

// 运行完整验证
console.log('🚀 开始个人信息积木修复完整验证...');

console.log('\n1️⃣ 检查BrickManager配置...');
const configResult = checkBrickManagerConfig();

console.log('\n2️⃣ 检查页面显示状态...');
const displayResult = verifyPersonalInfoFix();

console.log('\n📋 验证结果总结:');
if (configResult && displayResult) {
  console.log('🎉 修复验证完全成功！');
  console.log('💡 修复要点:');
  console.log('✅ 1. 移除了默认个人信息积木的生成');
  console.log('✅ 2. 前端直接使用云函数返回的积木数据');
  console.log('✅ 3. 当没有个人信息积木时，区域显示为空白');
  console.log('✅ 4. AI解析的个人信息正确生成积木');
} else {
  console.log('🔧 仍需进一步修复');
  console.log('💡 问题分析:');
  if (!configResult) {
    console.log('❌ BrickManager仍在生成默认积木');
  }
  if (!displayResult) {
    console.log('❌ 页面仍显示默认/占位积木');
  }
}
