/**
 * 个人信息模块修复验证脚本
 * 在微信开发者工具的Console中运行此脚本来验证修复效果
 */

// 测试个人信息模块修复
async function testPersonalInfoFix() {
  console.log('🧪 开始测试个人信息模块修复...');
  
  try {
    // 1. 清理旧数据
    console.log('🧹 清理旧数据...');
    wx.removeStorageSync('bricks');
    wx.removeStorageSync('bricks_sync_time');
    
    // 清理全局数据
    const app = getApp();
    if (app.globalData) {
      app.globalData.bricks = [];
    }
    
    // 2. 重新加载BrickManager
    console.log('🔄 重新加载BrickManager...');
    const BrickManager = require('utils/brick-manager.js');
    
    // 强制重新初始化
    BrickManager.bricks = [];
    BrickManager.initialized = false;
    
    // 3. 获取修复后的积木数据
    console.log('📦 获取修复后的积木数据...');
    const bricks = await BrickManager.getBricks();
    
    console.log('✅ 获取到积木数据:', bricks.length, '个');
    
    // 4. 验证个人信息积木
    const personalBricks = bricks.filter(brick => 
      brick.category === 'personal' || 
      brick.category === '个人' || 
      brick.category === '个人信息'
    );
    
    console.log('👤 个人信息积木数量:', personalBricks.length);
    
    if (personalBricks.length > 0) {
      const personalBrick = personalBricks[0];
      console.log('🔍 个人信息积木详情:');
      console.log('- ID:', personalBrick.id);
      console.log('- 标题:', personalBrick.title);
      console.log('- 描述:', personalBrick.description);
      console.log('- 分类:', personalBrick.category);
      console.log('- 关键词:', personalBrick.keywords);
      
      // 验证必要字段
      const hasTitle = !!personalBrick.title;
      const hasDescription = !!personalBrick.description;
      const hasCategory = !!personalBrick.category;
      
      console.log('✅ 字段验证结果:');
      console.log('- 标题字段:', hasTitle ? '✅ 存在' : '❌ 缺失');
      console.log('- 描述字段:', hasDescription ? '✅ 存在' : '❌ 缺失');
      console.log('- 分类字段:', hasCategory ? '✅ 存在' : '❌ 缺失');
      
      if (hasTitle && hasDescription && hasCategory) {
        console.log('🎉 个人信息积木修复成功！');
        return true;
      } else {
        console.log('❌ 个人信息积木仍有问题');
        return false;
      }
    } else {
      console.log('⚠️ 未找到个人信息积木');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 测试页面显示效果
function testPageDisplay() {
  console.log('🖥️ 测试页面显示效果...');
  
  try {
    // 获取当前页面实例
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route === 'pages/bricks/bricks') {
      console.log('✅ 当前在积木库页面');
      
      // 重新加载积木列表
      console.log('🔄 重新加载积木列表...');
      currentPage.loadBricksList();
      
      // 等待数据加载完成后检查
      setTimeout(() => {
        const bricks = currentPage.data.bricks || [];
        const personalCount = currentPage.data.personalCount || 0;
        const filteredBricks = currentPage.data.filteredBricks || [];
        
        console.log('📊 页面数据状态:');
        console.log('- 总积木数:', bricks.length);
        console.log('- 个人信息积木数:', personalCount);
        console.log('- 筛选后积木数:', filteredBricks.length);
        
        // 检查个人信息积木的显示数据
        const personalBricks = filteredBricks.filter(brick => 
          brick.category === 'personal'
        );
        
        if (personalBricks.length > 0) {
          const brick = personalBricks[0];
          console.log('👤 页面中的个人信息积木:');
          console.log('- 标题:', brick.title);
          console.log('- 描述:', brick.description);
          console.log('- 分类:', brick.category);
          
          if (brick.title && brick.description) {
            console.log('🎉 页面显示修复成功！');
          } else {
            console.log('❌ 页面显示仍有问题');
          }
        } else {
          console.log('⚠️ 页面中未找到个人信息积木');
        }
      }, 2000);
      
    } else {
      console.log('⚠️ 当前不在积木库页面，请先导航到积木库页面');
    }
    
  } catch (error) {
    console.error('❌ 页面显示测试失败:', error);
  }
}

// 完整测试流程
async function runCompleteTest() {
  console.log('🚀 开始完整测试流程...');
  
  // 1. 测试数据修复
  const dataFixed = await testPersonalInfoFix();
  
  if (dataFixed) {
    console.log('✅ 数据修复测试通过');
    
    // 2. 测试页面显示
    console.log('⏳ 等待2秒后测试页面显示...');
    setTimeout(() => {
      testPageDisplay();
    }, 2000);
    
  } else {
    console.log('❌ 数据修复测试失败，跳过页面显示测试');
  }
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testPersonalInfoFix,
    testPageDisplay,
    runCompleteTest
  };
}

console.log('📋 个人信息模块修复验证脚本已加载');
console.log('💡 使用方法:');
console.log('1. 运行 testPersonalInfoFix() 测试数据修复');
console.log('2. 运行 testPageDisplay() 测试页面显示');
console.log('3. 运行 runCompleteTest() 执行完整测试');
