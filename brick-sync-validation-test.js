/**
 * 积木数据同步验证测试
 * 验证修复后的syncPendingBricksToCloud功能
 * 特别测试"亚马逊运营经历"等积木的同步问题
 */

console.log('🔧 积木数据同步验证测试');
console.log('🎯 主要修复：添加数据验证和清理逻辑，解决"积木标题、内容和分类不能为空"错误');

async function runBrickSyncValidationTest() {
  console.log('🚀 开始积木数据同步验证测试...');
  console.log('=' .repeat(60));
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    overall: 'FAIL'
  };
  
  try {
    // 获取BrickManager实例
    let BrickManager;
    try {
      const app = getApp();
      if (app && app.brickManager) {
        BrickManager = app.brickManager;
        console.log('✅ 通过app.brickManager获取BrickManager');
      } else {
        const brickManagerModule = require('../../utils/brick-manager.js');
        BrickManager = brickManagerModule.instance;
        console.log('✅ 通过require导入BrickManager');
      }
    } catch (error) {
      console.error('❌ 无法获取BrickManager:', error);
      throw new Error('BrickManager加载失败');
    }
    
    // 测试1: 数据验证功能测试
    console.log('\n🧪 测试1: 数据验证功能测试');
    
    const testBricks = [
      // 有效积木
      {
        id: 'valid_brick_1',
        title: '亚马逊运营经历',
        content: '负责亚马逊平台的产品运营，包括商品上架、价格策略、库存管理等工作',
        category: 'project',
        description: '电商平台运营经验',
        tags: ['电商', '运营', '亚马逊']
      },
      // 缺少标题的积木
      {
        id: 'invalid_brick_1',
        title: '',
        content: '这是一个缺少标题的积木',
        category: 'skill'
      },
      // 缺少内容的积木
      {
        id: 'invalid_brick_2',
        title: '无效积木测试',
        content: '',
        category: 'project'
      },
      // 缺少分类的积木
      {
        id: 'invalid_brick_3',
        title: '另一个无效积木',
        content: '这个积木缺少分类',
        category: ''
      },
      // 包含无效值的积木
      {
        id: 'invalid_brick_4',
        title: '暂无积木数据',
        content: '暂无积木数据',
        category: 'skill'
      }
    ];
    
    let validCount = 0;
    let invalidCount = 0;
    
    for (const brick of testBricks) {
      const validation = BrickManager.validateBrickData(brick);
      if (validation.isValid) {
        validCount++;
        console.log(`✅ 积木验证通过: ${brick.title}`);
      } else {
        invalidCount++;
        console.log(`❌ 积木验证失败: ${brick.title || '无标题'}`, validation.errors);
      }
    }
    
    results.tests.validation = {
      success: validCount === 1 && invalidCount === 4, // 期望1个有效，4个无效
      validCount,
      invalidCount,
      expectedValid: 1,
      expectedInvalid: 4
    };
    
    console.log(`📊 验证结果: 有效${validCount}个, 无效${invalidCount}个`);
    console.log(`🎯 数据验证测试: ${results.tests.validation.success ? '✅ 通过' : '❌ 失败'}`);
    
    // 测试2: 数据清理功能测试
    console.log('\n🧪 测试2: 数据清理功能测试');
    
    const dirtyBrick = {
      id: 'dirty_brick',
      title: '  需要清理的积木标题  ',
      content: '  这是需要清理的内容  ',
      category: '  skill  ',
      description: '',
      tags: 'not_an_array',
      usageCount: 'invalid_number',
      isActive: 'true'
    };
    
    const cleanedBrick = BrickManager.sanitizeBrickData(dirtyBrick);
    
    const cleaningSuccess = (
      cleanedBrick.title === '需要清理的积木标题' &&
      cleanedBrick.content === '这是需要清理的内容' &&
      cleanedBrick.category === 'skill' &&
      Array.isArray(cleanedBrick.tags) &&
      typeof cleanedBrick.usageCount === 'number' &&
      typeof cleanedBrick.isActive === 'boolean'
    );
    
    results.tests.sanitization = {
      success: cleaningSuccess,
      original: dirtyBrick,
      cleaned: cleanedBrick
    };
    
    console.log('📊 数据清理结果:');
    console.log(`- 标题清理: "${dirtyBrick.title}" → "${cleanedBrick.title}"`);
    console.log(`- 内容清理: "${dirtyBrick.content}" → "${cleanedBrick.content}"`);
    console.log(`- 分类清理: "${dirtyBrick.category}" → "${cleanedBrick.category}"`);
    console.log(`🎯 数据清理测试: ${cleaningSuccess ? '✅ 通过' : '❌ 失败'}`);
    
    // 测试3: 模拟待同步积木测试
    console.log('\n🧪 测试3: 模拟待同步积木测试');
    
    try {
      // 添加一些待同步的积木到BrickManager
      const pendingBricks = [
        {
          id: 'pending_amazon_' + Date.now(),
          title: '亚马逊运营经历',
          content: '负责亚马逊平台的产品运营，包括商品上架、价格策略、库存管理等工作。通过数据分析优化产品表现，提升销售转化率。',
          category: 'project',
          description: '电商平台运营经验',
          tags: ['电商', '运营', '亚马逊', '数据分析'],
          syncStatus: 'pending'
        },
        {
          id: 'pending_invalid_' + Date.now(),
          title: '',  // 无效积木，应该被跳过
          content: '这是一个无效的积木',
          category: 'skill',
          syncStatus: 'pending'
        }
      ];
      
      // 将待同步积木添加到BrickManager
      pendingBricks.forEach(brick => {
        BrickManager.bricks.push(brick);
      });
      
      console.log(`📦 添加了${pendingBricks.length}个待同步积木（1个有效，1个无效）`);
      
      // 执行同步
      console.log('🔄 开始执行同步测试...');
      const syncResult = await BrickManager.syncPendingBricksToCloud();
      
      console.log('📊 同步结果:', syncResult);
      
      results.tests.sync = {
        success: syncResult && syncResult.successCount === 1 && syncResult.skipCount === 1,
        syncResult,
        expectedSuccess: 1,
        expectedSkip: 1
      };
      
      console.log(`🎯 同步测试: ${results.tests.sync.success ? '✅ 通过' : '❌ 失败'}`);
      
    } catch (error) {
      console.error('❌ 同步测试失败:', error);
      results.tests.sync = {
        success: false,
        error: error.message
      };
    }
    
    // 测试4: 验证同步后的数据状态
    console.log('\n🧪 测试4: 验证同步后的数据状态');
    
    try {
      // 检查是否还有待同步的积木
      const remainingPending = BrickManager.bricks.filter(brick => brick.syncStatus === 'pending');
      
      // 检查"亚马逊运营经历"积木是否成功同步
      const amazonBrick = BrickManager.bricks.find(brick => 
        brick.title === '亚马逊运营经历' && !brick.syncStatus
      );
      
      results.tests.postSync = {
        success: remainingPending.length === 0 && !!amazonBrick,
        remainingPendingCount: remainingPending.length,
        amazonBrickSynced: !!amazonBrick
      };
      
      console.log(`📊 剩余待同步积木: ${remainingPending.length}个`);
      console.log(`🔍 亚马逊运营经历积木同步状态: ${amazonBrick ? '✅ 已同步' : '❌ 未同步'}`);
      console.log(`🎯 同步后状态验证: ${results.tests.postSync.success ? '✅ 通过' : '❌ 失败'}`);
      
    } catch (error) {
      console.error('❌ 同步后状态验证失败:', error);
      results.tests.postSync = {
        success: false,
        error: error.message
      };
    }
    
    // 计算总体结果
    const testCount = Object.keys(results.tests).length;
    const successCount = Object.values(results.tests).filter(test => test.success).length;
    const successRate = (successCount / testCount) * 100;
    results.overall = successRate >= 75 ? 'PASS' : 'FAIL'; // 4个测试中至少3个通过
    
    console.log('\n' + '=' .repeat(60));
    console.log('📋 积木数据同步验证测试结果汇总');
    console.log(`🎯 总体结果: ${results.overall}`);
    console.log(`📊 成功率: ${successRate.toFixed(1)}%`);
    console.log(`✅ 通过测试: ${successCount}/${testCount}`);
    
    // 详细结果
    console.log('\n📋 详细测试结果:');
    const testNameMap = {
      validation: '数据验证功能',
      sanitization: '数据清理功能',
      sync: '积木同步功能',
      postSync: '同步后状态验证'
    };
    
    Object.entries(results.tests).forEach(([testName, result], index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${testNameMap[testName]}: ${result.success ? '通过' : '失败'}`);
      if (!result.success && result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    if (results.overall === 'PASS') {
      console.log('\n🎉 🎉 🎉 恭喜！积木数据同步验证问题已彻底解决！🎉 🎉 🎉');
      console.log('✅ 数据验证功能正常');
      console.log('✅ 数据清理功能正常');
      console.log('✅ 积木同步功能正常');
      console.log('✅ "亚马逊运营经历"等积木能正确同步');
      console.log('✅ 无效积木数据被正确跳过');
      console.log('\n🚀 syncPendingBricksToCloud功能已修复！');
      console.log('🎊 "积木标题、内容和分类不能为空"错误已解决！');
      
    } else {
      console.log('\n⚠️ 部分测试失败，需要进一步检查');
      console.log('🔍 请查看详细测试结果');
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ 积木同步验证测试执行失败:', error);
    results.error = error.message;
    results.overall = 'ERROR';
    return results;
  }
}

// 执行测试
console.log('🎯 准备执行积木数据同步验证测试...');
runBrickSyncValidationTest().then(result => {
  console.log('\n🏁 积木数据同步验证测试执行完成！');
  
  if (result.overall === 'PASS') {
    console.log('🎊 积木数据同步验证问题已彻底解决！');
    console.log('🚀 "亚马逊运营经历"等积木现在可以正常同步了！');
  } else if (result.overall === 'ERROR') {
    console.log('🔧 测试执行遇到错误，请检查环境配置。');
  } else {
    console.log('🔧 部分功能需要进一步优化。');
  }
}).catch(error => {
  console.error('💥 测试执行异常:', error);
});

console.log('\n📖 使用说明:');
console.log('1. 确保在微信开发者工具中运行');
console.log('2. 复制此脚本到控制台执行');
console.log('3. 查看测试结果，验证同步功能修复效果');
console.log('4. 如果测试通过，"积木标题、内容和分类不能为空"错误已解决');
