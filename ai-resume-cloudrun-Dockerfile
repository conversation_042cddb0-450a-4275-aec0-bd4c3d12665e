# syntax=docker/dockerfile:1
FROM node:18-slim

# 设置工作目录
WORKDIR /app

# 安装 Puppeteer 所需的系统依赖
RUN apt-get update && apt-get install -y \
    chromium \
    curl \
    fonts-liberation \
    fonts-noto-color-emoji \
    fonts-noto-cjk \
    fonts-wqy-zenhei \
    fonts-wqy-microhei \
    libasound2 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libgconf-2-4 \
    xdg-utils \
    ca-certificates \
    --no-install-recommends && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean

# 设置 Puppeteer 环境变量
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium \
    NODE_ENV=production \
    PORT=80

# 复制 package 文件并安装依赖
COPY package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force

# 复制应用代码
COPY . .

# 创建非root用户以提高安全性
RUN groupadd -r pptruser && \
    useradd -r -g pptruser -G audio,video pptruser && \
    mkdir -p /home/<USER>/Downloads && \
    chown -R pptruser:pptruser /home/<USER>
    chown -R pptruser:pptruser /app

# 切换到非root用户
USER pptruser

# 暴露端口（与 cloudbaserc.json 中的 containerPort 一致）
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# 启动应用
CMD ["node", "index.js"]
