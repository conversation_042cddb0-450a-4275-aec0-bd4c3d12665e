/**
 * 积木页面性能优化应用脚本
 * 用于将优化后的函数应用到原始文件中
 */

// 导入优化后的函数
const {
    optimizedSetFilter,
    optimizedFilterBricks,
    optimizedLoadBricksList,
    optimizedUpdateCounts
} = require('./optimized-bricks-functions.js');

// 导入文件系统模块
const fs = require('fs');
const path = require('path');

// 原始文件路径
const originalFilePath = path.join(__dirname, 'pages/bricks/bricks.js');
// 备份文件路径
const backupFilePath = path.join(__dirname, 'pages/bricks/bricks.js.bak');

// 读取原始文件
console.log('读取原始文件...');
let content = fs.readFileSync(originalFilePath, 'utf8');

// 创建备份
console.log('创建备份文件...');
fs.writeFileSync(backupFilePath, content, 'utf8');

// 替换函数
console.log('开始替换函数...');

// 1. 替换 setFilter 函数
console.log('替换 setFilter 函数...');
content = content.replace(
    /setFilter\(e\)\s*\{[\s\S]*?this\.filterBricks\(\)[\s\S]*?this\.filterResumes\(\)[\s\S]*?\}/,
    optimizedSetFilter.toString()
);

// 2. 替换 filterBricks 函数
console.log('替换 filterBricks 函数...');
content = content.replace(
    /filterBricks\(\)\s*\{[\s\S]*?this\.setData\(\{\s*filteredBricks[\s\S]*?\}\)/,
    optimizedFilterBricks.toString()
);

// 3. 替换 loadBricksList 函数
console.log('替换 loadBricksList 函数...');
content = content.replace(
    /async\s+loadBricksList\(\)\s*\{[\s\S]*?this\.setData\(\{[\s\S]*?loading:\s*false[\s\S]*?\}\)[\s\S]*?\}/,
    optimizedLoadBricksList.toString()
);

// 4. 替换 updateCounts 函数
console.log('替换 updateCounts 函数...');
content = content.replace(
    /updateCounts\(\)\s*\{[\s\S]*?this\.setData\(\{[\s\S]*?resumeCount[\s\S]*?\}\)[\s\S]*?\}/,
    optimizedUpdateCounts.toString()
);

// 添加缓存相关的初始化代码
console.log('添加缓存初始化代码...');
const cacheInitCode = `
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化缓存对象
    this.filterCache = {};
    this.bricksDataCache = null;
    this.bricksDataCacheTime = 0;
    
    console.log('积木页面加载，初始化缓存机制');
    this.initPage();
  }`;

content = content.replace(
    /\/\*\*\s*\n\s*\*\s*生命周期函数--监听页面加载\s*\n\s*\*\/\s*\n\s*onLoad\(options\)\s*\{[\s\S]*?this\.initPage\(\);?\s*\}/,
    cacheInitCode
);

// 写入修改后的文件
console.log('写入修改后的文件...');
fs.writeFileSync(originalFilePath, content, 'utf8');

console.log('优化完成！原始文件已备份为 bricks.js.bak');