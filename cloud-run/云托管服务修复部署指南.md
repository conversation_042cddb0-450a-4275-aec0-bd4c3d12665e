# 云托管服务修复部署指南

## 问题分析

当前云托管服务返回503错误，主要原因：
1. 容器启动失败或配置不当
2. Puppeteer在容器环境中的兼容性问题
3. 缺少必要的系统依赖

## 修复内容

### 1. 优化Dockerfile
- 添加了更多Puppeteer所需的系统依赖
- 增加了中文字体支持
- 添加了健康检查机制
- 创建了非root用户运行服务

### 2. 改进Puppeteer配置
- 针对Linux容器环境优化启动参数
- 添加了重试机制
- 改进了错误处理

### 3. 增强稳定性
- 添加了更多Chrome启动参数
- 优化了超时设置
- 改进了日志输出

## 部署步骤

### 方法一：使用微信云托管控制台（推荐）

1. **访问微信云托管控制台**
   ```
   https://cloud.weixin.qq.com/cloudrun
   ```

2. **选择环境**
   - 选择对应的环境（通常是prod）

3. **找到现有服务**
   - 找到 `ai-resume-snapshot` 或 `resume-snapshot` 服务
   - 如果不存在，创建新服务

4. **上传新版本**
   - 点击"新建版本"
   - 上传文件：`resume-snapshot-fixed-20250729-181831.tar.gz`
   - 等待构建完成

5. **配置服务参数**
   ```
   服务名称: ai-resume-snapshot
   端口: 80
   最小实例: 0
   最大实例: 5
   CPU: 1核
   内存: 2GB
   环境变量:
     - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
     - NODE_ENV=production
   ```

6. **发布版本**
   - 构建成功后，点击"发布"
   - 等待服务启动

### 方法二：使用腾讯云云托管控制台

1. **访问腾讯云控制台**
   ```
   https://console.cloud.tencent.com/tcb/service
   ```

2. **选择环境**
   - 选择环境ID: `zemuresume-4gjvx1wea78e3d1e`

3. **部署服务**
   - 按照相同的配置参数部署

## 验证部署

### 1. 健康检查
部署完成后，访问健康检查接口：
```bash
curl https://your-service-url/health
```

期望返回：
```json
{"status":"healthy","timestamp":"2025-07-29T10:20:00.000Z"}
```

### 2. 截图功能测试
```bash
curl -X POST https://your-service-url/snapshot \
  -H "Content-Type: application/json" \
  -d '{"url":"https://example.com","format":"png","width":800,"height":600}' \
  --output test.png
```

### 3. 检查日志
在云托管控制台查看服务日志，确保：
- 服务启动成功
- Puppeteer浏览器启动正常
- 没有错误信息

## 常见问题排查

### 1. 服务启动失败
- 检查Dockerfile语法
- 确认所有依赖已安装
- 查看启动日志

### 2. Puppeteer启动失败
- 确认PUPPETEER_EXECUTABLE_PATH环境变量
- 检查Chrome依赖是否完整
- 查看浏览器启动日志

### 3. 截图生成失败
- 检查目标URL是否可访问
- 确认网络连接正常
- 查看详细错误信息

## 修复后的关键改进

1. **更完整的依赖**
   - 添加了libgbm1、libxss1等关键依赖
   - 包含了中文字体支持

2. **更安全的运行环境**
   - 使用非root用户运行
   - 添加了安全相关的Chrome参数

3. **更好的错误处理**
   - 重试机制
   - 详细的日志输出
   - 优雅的错误处理

4. **健康检查**
   - Docker健康检查
   - HTTP健康检查接口

## 预期结果

部署成功后，云托管服务应该：
- ✅ 健康检查返回200状态
- ✅ 截图接口正常工作
- ✅ 不再返回503错误
- ✅ 支持AI简历系统的预览功能

## 下一步

部署完成后，运行端到端测试验证整个AI简历系统功能正常。
