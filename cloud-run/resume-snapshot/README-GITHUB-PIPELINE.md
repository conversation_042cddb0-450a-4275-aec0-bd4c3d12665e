# AI简历系统 - GitHub CI/CD 流水线部署指南

## 概述

本指南将帮助您设置从GitHub到微信云托管的自动化部署流水线，实现代码推送后自动部署到云托管服务。

## 架构说明

```
GitHub Repository
    ↓ (git push)
GitHub Actions
    ↓ (自动构建)
WeChat CloudRun
    ↓ (服务运行)
AI Resume Service
```

## 设置步骤

### 1. 创建GitHub仓库

1. 在GitHub上创建新仓库 `ai-resume`
2. 将本地代码推送到GitHub：

```bash
cd /Users/<USER>/Desktop/ai-resume
git init
git add .
git commit -m "Initial commit: AI Resume System"
git branch -M main
git remote add origin https://github.com/xuyuzeamazon/ai-resume.git
git push -u origin main
```

### 2. 配置GitHub Secrets

在GitHub仓库的 Settings > Secrets and variables > Actions 中添加以下密钥：

#### 必需的Secrets：

1. **CLOUDBASE_SECRET_ID**
   - 值：您的腾讯云SecretId
   - 获取方式：腾讯云控制台 > 访问管理 > API密钥管理

2. **CLOUDBASE_SECRET_KEY**
   - 值：您的腾讯云SecretKey
   - 获取方式：腾讯云控制台 > 访问管理 > API密钥管理

3. **CLOUDBASE_ENV_ID**
   - 值：`zemuresume-4gjvx1wea78e3d1e`
   - 说明：您的云开发环境ID

### 3. 验证配置

#### 3.1 检查cloudbaserc.json
确保项目根目录有正确的配置文件：

```json
{
    "envId": "zemuresume-4gjvx1wea78e3d1e",
    "version": "2.0",
    "framework": {
        "name": "resume-snapshot",
        "plugins": {
            "container": {
                "use": "@cloudbase/framework-plugin-container",
                "inputs": {
                    "serviceName": "ai-resume-snapshot",
                    "servicePath": "/resume-snapshot",
                    "containerPort": 80
                }
            }
        }
    }
}
```

#### 3.2 检查package.json
确保有必要的脚本：

```json
{
  "scripts": {
    "start": "node index.js",
    "test": "echo \"No tests specified\" && exit 0",
    "lint": "echo \"No linting configured\" && exit 0"
  }
}
```

### 4. 触发部署

#### 4.1 自动触发
- 推送代码到 `main` 或 `master` 分支
- 修改 `cloud-run/resume-snapshot/` 目录下的文件

#### 4.2 手动触发
在GitHub仓库的 Actions 页面手动运行工作流

### 5. 监控部署

#### 5.1 GitHub Actions日志
- 访问：`https://github.com/xuyuzeamazon/ai-resume/actions`
- 查看每次部署的详细日志

#### 5.2 服务健康检查
- 健康检查URL：`https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/health`
- 预期响应：`{"status":"healthy","timestamp":"..."}`

#### 5.3 截图服务测试
```bash
curl -X POST https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/snapshot \
  -H "Content-Type: application/json" \
  -d '{"url":"https://example.com","format":"png","width":800,"height":600}' \
  --output test.png
```

## 工作流程说明

### 部署流程
1. **代码检出**: 获取最新代码
2. **环境准备**: 安装Node.js和依赖
3. **代码检查**: 运行lint和测试
4. **CLI安装**: 安装云开发CLI工具
5. **环境配置**: 设置云开发环境
6. **服务部署**: 部署到微信云托管
7. **健康检查**: 验证服务正常运行
8. **功能测试**: 测试截图服务
9. **结果通知**: 报告部署结果

### 触发条件
- 推送到主分支（main/master）
- 修改云托管相关文件
- 手动触发

### 环境变量
- `NODE_ENV=production`
- `PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium`

## 故障排除

### 常见问题

#### 1. 部署失败
- 检查GitHub Secrets是否正确配置
- 查看Actions日志中的错误信息
- 确认云开发环境ID正确

#### 2. 服务启动失败
- 检查Dockerfile配置
- 确认依赖安装正确
- 查看云托管控制台日志

#### 3. 健康检查失败
- 等待更长时间（服务启动需要时间）
- 检查端口配置（80端口）
- 确认路由配置正确

### 调试方法

#### 1. 本地测试
```bash
cd cloud-run/resume-snapshot
npm install
npm start
curl http://localhost:8080/health
```

#### 2. 查看部署日志
- GitHub Actions页面查看详细日志
- 微信云托管控制台查看服务日志

#### 3. 手动部署验证
```bash
cd cloud-run/resume-snapshot
npx @cloudbase/framework-cli deploy
```

## 最佳实践

### 1. 代码管理
- 使用分支保护规则
- 要求PR审查
- 自动化测试

### 2. 部署策略
- 蓝绿部署
- 滚动更新
- 回滚机制

### 3. 监控告警
- 设置健康检查
- 配置错误告警
- 性能监控

## 相关链接

- [微信云托管文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloudrun/)
- [GitHub Actions文档](https://docs.github.com/en/actions)
- [云开发CLI文档](https://docs.cloudbase.net/cli/intro.html)

## 支持

如有问题，请：
1. 查看GitHub Actions日志
2. 检查微信云托管控制台
3. 参考故障排除部分
4. 联系技术支持
