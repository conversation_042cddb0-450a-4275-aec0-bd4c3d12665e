/**
 * 云托管服务功能测试脚本
 * 用于验证修复后的代码是否能正常工作
 */

const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
    // 云托管服务地址
    cloudRunUrl: 'https://ai-resume-snapshot2-176277-5-1341667342.sh.run.tcloudbase.com',
    
    // 测试超时时间
    timeout: 30000,
    
    // 重试次数
    maxRetries: 3
};

/**
 * 发送HTTP请求
 */
async function makeRequest(url, options = {}) {
    const fetch = (await import('node-fetch')).default;
    
    const startTime = Date.now();
    
    try {
        console.log(`📡 发送请求: ${options.method || 'GET'} ${url}`);
        
        const response = await fetch(url, {
            timeout: TEST_CONFIG.timeout,
            ...options
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        const result = {
            ok: response.ok,
            status: response.status,
            statusText: response.statusText,
            responseTime: responseTime,
            headers: Object.fromEntries(response.headers.entries())
        };
        
        // 根据Content-Type处理响应数据
        const contentType = response.headers.get('content-type') || '';
        
        if (contentType.includes('application/json')) {
            result.data = await response.json();
        } else if (contentType.includes('image/') || contentType.includes('application/pdf')) {
            result.data = await response.buffer();
        } else {
            result.data = await response.text();
        }
        
        console.log(`⏱️ 响应时间: ${responseTime}ms, 状态: ${response.status}`);
        
        return result;
        
    } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        console.error(`❌ 请求失败: ${error.message}`);
        
        return {
            ok: false,
            error: error.message,
            responseTime: responseTime
        };
    }
}

/**
 * 测试健康检查
 */
async function testHealthCheck() {
    console.log('\n🔍 测试云托管服务健康检查...');
    
    let retries = TEST_CONFIG.maxRetries;
    
    while (retries > 0) {
        const result = await makeRequest(`${TEST_CONFIG.cloudRunUrl}/health`);
        
        if (result.ok) {
            console.log('✅ 健康检查通过:', result.data);
            return { success: true, data: result.data, responseTime: result.responseTime };
        } else {
            console.log(`❌ 健康检查失败 (重试 ${TEST_CONFIG.maxRetries - retries + 1}/${TEST_CONFIG.maxRetries}):`, result.error || result.statusText);
            retries--;
            
            if (retries > 0) {
                console.log('⏳ 等待5秒后重试...');
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }
    }
    
    return { success: false, error: '健康检查失败，已重试3次' };
}

/**
 * 测试截图功能
 */
async function testSnapshotFunction() {
    console.log('\n📸 测试云托管截图功能...');
    
    const testData = {
        url: 'https://example.com',
        format: 'png',
        width: 800,
        height: 600,
        options: {
            fullPage: false,
            quality: 90,
            delay: 1000
        }
    };
    
    const result = await makeRequest(`${TEST_CONFIG.cloudRunUrl}/snapshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    });
    
    if (result.ok && result.data) {
        // 保存截图文件
        const outputDir = path.join(__dirname, 'test-output');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        const filename = `cloud-service-test-${Date.now()}.png`;
        const filepath = path.join(outputDir, filename);
        
        fs.writeFileSync(filepath, result.data);
        
        console.log('✅ 截图功能测试成功:');
        console.log(`   文件大小: ${result.data.length} bytes`);
        console.log(`   保存路径: ${filepath}`);
        console.log(`   响应时间: ${result.responseTime}ms`);
        
        return { 
            success: true, 
            filepath: filepath, 
            fileSize: result.data.length,
            responseTime: result.responseTime 
        };
    } else {
        console.error('❌ 截图功能测试失败:', result.error || result.statusText);
        if (result.data && typeof result.data === 'string') {
            try {
                const errorData = JSON.parse(result.data);
                console.log('   错误详情:', errorData);
            } catch (e) {
                console.log('   错误信息:', result.data);
            }
        }
        return { success: false, error: result.error || result.statusText };
    }
}

/**
 * 测试简历HTML截图
 */
async function testResumeHtmlSnapshot() {
    console.log('\n📄 测试简历HTML截图...');
    
    // 使用一个简单的HTML页面进行测试
    const testHtmlUrl = 'https://zemuresume-4gjvx1wea78e3d1e-1341667342.tcloudbaseapp.com/resume-html/resume-html-1753775173620.html';
    
    const testData = {
        url: testHtmlUrl,
        format: 'png',
        width: 1240,
        height: 1754,
        options: {
            fullPage: true,
            quality: 90,
            delay: 2000,
            deviceScaleFactor: 1
        }
    };
    
    const result = await makeRequest(`${TEST_CONFIG.cloudRunUrl}/snapshot`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    });
    
    if (result.ok && result.data) {
        const outputDir = path.join(__dirname, 'test-output');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        const filename = `resume-html-test-${Date.now()}.png`;
        const filepath = path.join(outputDir, filename);
        
        fs.writeFileSync(filepath, result.data);
        
        console.log('✅ 简历HTML截图测试成功:');
        console.log(`   文件大小: ${result.data.length} bytes`);
        console.log(`   保存路径: ${filepath}`);
        console.log(`   响应时间: ${result.responseTime}ms`);
        
        return { 
            success: true, 
            filepath: filepath, 
            fileSize: result.data.length,
            responseTime: result.responseTime 
        };
    } else {
        console.error('❌ 简历HTML截图测试失败:', result.error || result.statusText);
        return { success: false, error: result.error || result.statusText };
    }
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
    const report = {
        timestamp: new Date().toISOString(),
        cloudRunUrl: TEST_CONFIG.cloudRunUrl,
        summary: {
            totalTests: results.length,
            passedTests: results.filter(r => r.success).length,
            failedTests: results.filter(r => !r.success).length,
            successRate: 0,
            averageResponseTime: 0
        },
        details: results
    };
    
    report.summary.successRate = report.summary.passedTests / report.summary.totalTests;
    
    const responseTimes = results.filter(r => r.responseTime).map(r => r.responseTime);
    report.summary.averageResponseTime = responseTimes.length > 0 
        ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
        : 0;
    
    return report;
}

/**
 * 主测试函数
 */
async function runCloudServiceTest() {
    console.log('🚀 开始云托管服务功能测试\n');
    console.log(`🌐 服务地址: ${TEST_CONFIG.cloudRunUrl}\n`);
    
    const results = [];
    
    try {
        // 1. 测试健康检查
        const healthResult = await testHealthCheck();
        results.push({
            test: '健康检查',
            success: healthResult.success,
            error: healthResult.error,
            responseTime: healthResult.responseTime
        });
        
        // 如果健康检查失败，跳过其他测试
        if (!healthResult.success) {
            console.log('\n❌ 健康检查失败，跳过其他测试');
        } else {
            // 2. 测试基本截图功能
            const snapshotResult = await testSnapshotFunction();
            results.push({
                test: '基本截图功能',
                success: snapshotResult.success,
                error: snapshotResult.error,
                responseTime: snapshotResult.responseTime,
                fileSize: snapshotResult.fileSize
            });
            
            // 3. 测试简历HTML截图
            const resumeResult = await testResumeHtmlSnapshot();
            results.push({
                test: '简历HTML截图',
                success: resumeResult.success,
                error: resumeResult.error,
                responseTime: resumeResult.responseTime,
                fileSize: resumeResult.fileSize
            });
        }
        
        // 生成测试报告
        const report = generateTestReport(results);
        
        // 保存测试报告
        const reportPath = path.join(__dirname, 'test-output', `cloud-service-test-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n📊 测试完成！');
        console.log(`📄 测试报告: ${reportPath}`);
        console.log(`✅ 成功率: ${(report.summary.successRate * 100).toFixed(1)}%`);
        console.log(`⏱️ 平均响应时间: ${report.summary.averageResponseTime.toFixed(0)}ms`);
        
        if (report.summary.successRate === 1) {
            console.log('🎉 所有测试通过！云托管服务工作正常。');
        } else {
            console.log('⚠️ 部分测试失败，需要进一步检查。');
        }
        
        return report;
        
    } catch (error) {
        console.error('💥 测试过程中发生错误:', error);
        return null;
    }
}

// 运行测试
if (require.main === module) {
    runCloudServiceTest().catch(console.error);
}

module.exports = {
    runCloudServiceTest,
    testHealthCheck,
    testSnapshotFunction,
    testResumeHtmlSnapshot
};
