#!/bin/bash

# AI简历系统 - GitHub自动化部署脚本
# 用于本地测试和验证部署流程

set -e  # 遇到错误立即退出

echo "🚀 开始AI简历系统GitHub部署流程..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message $RED "❌ $1 命令未找到，请先安装"
        exit 1
    fi
}

# 1. 环境检查
print_message $BLUE "📋 1. 检查环境依赖..."
check_command "node"
check_command "npm"
check_command "git"
check_command "curl"

print_message $GREEN "✅ 环境检查通过"

# 2. 项目信息
print_message $BLUE "📋 2. 项目信息..."
echo "项目目录: $(pwd)"
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"
echo "Git版本: $(git --version)"

# 3. 安装依赖
print_message $BLUE "📦 3. 安装项目依赖..."
if [ -f "package.json" ]; then
    npm install
    print_message $GREEN "✅ 依赖安装完成"
else
    print_message $RED "❌ 未找到package.json文件"
    exit 1
fi

# 4. 代码检查
print_message $BLUE "🔍 4. 代码检查..."
if [ -f "index.js" ]; then
    # 基本语法检查
    node -c index.js
    print_message $GREEN "✅ 代码语法检查通过"
else
    print_message $RED "❌ 未找到index.js文件"
    exit 1
fi

# 5. 本地测试
print_message $BLUE "🧪 5. 本地服务测试..."
echo "启动本地服务进行测试..."

# 启动服务（后台运行）
PORT=8080 npm start &
SERVER_PID=$!

# 等待服务启动
sleep 5

# 健康检查
print_message $YELLOW "⏳ 进行健康检查..."
if curl -f -s http://localhost:8080/health > /dev/null; then
    print_message $GREEN "✅ 本地服务健康检查通过"
else
    print_message $RED "❌ 本地服务健康检查失败"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# 停止本地服务
kill $SERVER_PID 2>/dev/null || true
print_message $GREEN "✅ 本地测试完成"

# 6. Git状态检查
print_message $BLUE "📝 6. Git状态检查..."
if git status --porcelain | grep -q .; then
    print_message $YELLOW "⚠️ 有未提交的更改："
    git status --short
    echo ""
    read -p "是否继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_message $YELLOW "🛑 部署已取消"
        exit 0
    fi
else
    print_message $GREEN "✅ Git工作区干净"
fi

# 7. 检查GitHub配置
print_message $BLUE "🔧 7. 检查GitHub配置..."

# 检查是否有GitHub远程仓库
if git remote get-url origin &> /dev/null; then
    REPO_URL=$(git remote get-url origin)
    print_message $GREEN "✅ GitHub远程仓库: $REPO_URL"
else
    print_message $RED "❌ 未配置GitHub远程仓库"
    echo "请先添加GitHub远程仓库："
    echo "git remote add origin https://github.com/xuyuzeamazon/ai-resume.git"
    exit 1
fi

# 检查当前分支
CURRENT_BRANCH=$(git branch --show-current)
print_message $BLUE "📍 当前分支: $CURRENT_BRANCH"

# 8. 推送到GitHub
print_message $BLUE "📤 8. 推送代码到GitHub..."
echo "准备推送到分支: $CURRENT_BRANCH"

read -p "确认推送代码到GitHub？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git add .
    
    # 检查是否有更改需要提交
    if git diff --staged --quiet; then
        print_message $YELLOW "⚠️ 没有新的更改需要提交"
    else
        # 提交更改
        COMMIT_MSG="Deploy: AI Resume System $(date '+%Y-%m-%d %H:%M:%S')"
        git commit -m "$COMMIT_MSG"
        print_message $GREEN "✅ 代码已提交: $COMMIT_MSG"
    fi
    
    # 推送到GitHub
    git push origin $CURRENT_BRANCH
    print_message $GREEN "✅ 代码已推送到GitHub"
    
    # 提供GitHub Actions链接
    REPO_NAME=$(basename -s .git $(git remote get-url origin))
    GITHUB_USER=$(git remote get-url origin | sed -n 's/.*github.com[:/]\([^/]*\)\/.*/\1/p')
    ACTIONS_URL="https://github.com/$GITHUB_USER/$REPO_NAME/actions"
    
    print_message $BLUE "🔗 GitHub Actions: $ACTIONS_URL"
    
else
    print_message $YELLOW "🛑 推送已取消"
    exit 0
fi

# 9. 等待部署完成
print_message $BLUE "⏳ 9. 等待GitHub Actions部署..."
echo "请访问GitHub Actions页面查看部署进度："
echo "$ACTIONS_URL"
echo ""
echo "部署完成后，可以通过以下方式验证："
echo "1. 健康检查: https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/health"
echo "2. 截图服务测试:"
echo "   curl -X POST https://ai-resume-snapshot2-176277-5-**********.sh.run.tcloudbase.com/snapshot \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"url\":\"https://example.com\",\"format\":\"png\",\"width\":800,\"height\":600}' \\"
echo "     --output test.png"

# 10. 完成
print_message $GREEN "🎉 GitHub部署流程启动完成！"
print_message $BLUE "📋 后续步骤："
echo "1. 在GitHub Actions页面监控部署进度"
echo "2. 部署完成后进行功能验证"
echo "3. 如有问题，查看Actions日志进行调试"

print_message $YELLOW "💡 提示："
echo "- 首次部署可能需要5-10分钟"
echo "- 确保GitHub Secrets已正确配置"
echo "- 如需回滚，可以推送之前的代码版本"
