/**
 * 验证积木数据持久化修复效果
 * 模拟generate.js通过BrickManager获取积木数据的场景
 */

console.log('🔧 开始验证积木数据持久化修复效果...');

/**
 * 测试1: 验证BrickManager能否正确获取云数据库中的积木
 */
async function testBrickManagerGetBricks() {
  console.log('\n📋 测试1: 验证BrickManager.getBricks()方法');
  
  try {
    // 模拟generate.js中的调用方式
    const { instance: BrickManager } = require('./utils/brick-manager.js');
    
    console.log('🔄 调用BrickManager.getBricks()...');
    const bricks = await BrickManager.getBricks();
    
    console.log(`✅ 获取到积木数量: ${bricks.length}`);
    console.log('📊 积木详情:');
    bricks.forEach((brick, index) => {
      console.log(`  ${index + 1}. ${brick.title} (${brick.category})`);
    });
    
    // 验证是否达到预期数量（应该至少有3个测试积木）
    const success = bricks.length >= 3;
    console.log(`🎯 测试结果: ${success ? '✅ 通过' : '❌ 失败'} (期望≥3个，实际${bricks.length}个)`);
    
    return {
      success,
      count: bricks.length,
      bricks: bricks.map(b => ({ id: b.id, title: b.title, category: b.category }))
    };
    
  } catch (error) {
    console.error('❌ 测试1失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试2: 验证批量保存功能
 */
async function testBatchSave() {
  console.log('\n📋 测试2: 验证批量保存功能');
  
  try {
    const { instance: BrickManager } = require('./utils/brick-manager.js');
    
    // 模拟bricks.js解析出的新积木数据
    const newBricks = [
      {
        id: 'fix_test_1',
        title: '修复测试积木1',
        description: '用于验证修复效果的测试积木',
        content: '这是一个用于验证积木数据持久化修复效果的测试积木',
        category: 'skill',
        tags: ['修复测试', '持久化'],
        usageCount: 0,
        createTime: new Date().toISOString()
      },
      {
        id: 'fix_test_2',
        title: '修复测试积木2',
        description: '另一个测试积木',
        content: '验证批量保存功能是否正常工作',
        category: 'project',
        tags: ['批量保存', '测试'],
        usageCount: 0,
        createTime: new Date().toISOString()
      }
    ];
    
    console.log(`🔄 调用BrickManager.addBricksBatch()保存${newBricks.length}个积木...`);
    const result = await BrickManager.addBricksBatch(newBricks);
    
    console.log(`✅ 批量保存完成，返回${result.length}个积木`);
    
    // 验证保存后能否重新获取
    console.log('🔄 重新获取积木验证保存效果...');
    const allBricks = await BrickManager.getBricks();
    
    const success = allBricks.length >= 5; // 原有3个 + 新增2个
    console.log(`🎯 测试结果: ${success ? '✅ 通过' : '❌ 失败'} (期望≥5个，实际${allBricks.length}个)`);
    
    return {
      success,
      savedCount: result.length,
      totalCount: allBricks.length
    };
    
  } catch (error) {
    console.error('❌ 测试2失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试3: 验证数据持久性（刷新后数据是否还在）
 */
async function testDataPersistence() {
  console.log('\n📋 测试3: 验证数据持久性');
  
  try {
    const { instance: BrickManager } = require('./utils/brick-manager.js');
    
    // 强制刷新BrickManager，模拟页面刷新
    console.log('🔄 强制刷新BrickManager...');
    await BrickManager.refresh();
    
    const bricks = await BrickManager.getBricks();
    console.log(`✅ 刷新后获取到积木数量: ${bricks.length}`);
    
    // 检查是否包含我们之前保存的测试积木
    const hasFixTestBricks = bricks.some(b => b.id.startsWith('fix_test_'));
    const hasOriginalBricks = bricks.some(b => b.id.startsWith('test_batch_'));
    
    const success = bricks.length >= 5 && hasFixTestBricks && hasOriginalBricks;
    console.log(`🎯 测试结果: ${success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`  - 总积木数量: ${bricks.length}`);
    console.log(`  - 包含修复测试积木: ${hasFixTestBricks ? '✅' : '❌'}`);
    console.log(`  - 包含原始测试积木: ${hasOriginalBricks ? '✅' : '❌'}`);
    
    return {
      success,
      count: bricks.length,
      hasFixTestBricks,
      hasOriginalBricks
    };
    
  } catch (error) {
    console.error('❌ 测试3失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 运行完整验证测试
 */
async function runVerificationTest() {
  console.log('🚀 开始运行积木数据持久化修复验证测试...');
  console.log('=' .repeat(60));
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  };
  
  try {
    // 运行所有测试
    results.tests.getBricks = await testBrickManagerGetBricks();
    results.tests.batchSave = await testBatchSave();
    results.tests.persistence = await testDataPersistence();
    
    // 计算总体成功率
    const testCount = Object.keys(results.tests).length;
    const successCount = Object.values(results.tests).filter(test => test.success).length;
    results.successRate = (successCount / testCount) * 100;
    results.overall = results.successRate >= 95 ? 'PASS' : 'FAIL';
    
    console.log('\n' + '=' .repeat(60));
    console.log('📋 验证测试完成');
    console.log(`🎯 总体结果: ${results.overall}`);
    console.log(`📊 成功率: ${results.successRate}%`);
    console.log(`✅ 通过测试: ${successCount}/${testCount}`);
    
    if (results.overall === 'PASS') {
      console.log('🎉 积木数据持久化修复成功！');
      console.log('✅ generate.js现在应该能正确获取到积木数据了');
    } else {
      console.log('⚠️ 修复效果不理想，需要进一步调试');
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ 验证测试运行失败:', error);
    results.error = error.message;
    results.overall = 'ERROR';
    return results;
  }
}

// 如果直接运行此脚本
if (typeof module !== 'undefined' && require.main === module) {
  console.log('⚠️ 此脚本需要在微信小程序环境中运行');
  console.log('📝 请在微信开发者工具控制台中执行:');
  console.log('require("./test-fix-verification.js").runVerificationTest()');
}

// 导出测试函数
module.exports = {
  testBrickManagerGetBricks,
  testBatchSave,
  testDataPersistence,
  runVerificationTest
};
