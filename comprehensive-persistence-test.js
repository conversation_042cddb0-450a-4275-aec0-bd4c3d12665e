/**
 * 综合数据持久化和页面切换测试
 * 验证BrickManager的完整功能和数据在页面切换时的持久性
 * 必须在微信小程序环境中运行
 */

console.log('🔧 综合数据持久化和页面切换测试');
console.log('📱 环境检查:', typeof wx !== 'undefined' ? '✅ 微信小程序' : '❌ 非微信环境');

if (typeof wx === 'undefined') {
  console.error('❌ 请在微信开发者工具控制台中运行此测试');
} else {
  
  // 测试数据
  const testBricks = [
    {
      id: 'persist_test_1',
      title: '持久化测试积木1',
      description: '验证数据持久化功能',
      content: '这是用于验证数据持久化功能的测试积木，应该在页面切换后仍然存在',
      category: 'skill',
      tags: ['持久化', '测试'],
      usageCount: 0
    },
    {
      id: 'persist_test_2',
      title: '持久化测试积木2',
      description: '验证页面切换数据保持',
      content: '这是第二个测试积木，用于验证页面切换后数据是否保持',
      category: 'project',
      tags: ['页面切换', '数据保持'],
      usageCount: 0
    },
    {
      id: 'persist_test_3',
      title: '持久化测试积木3',
      description: '验证云数据库同步',
      content: '这是第三个测试积木，用于验证云数据库同步功能',
      category: 'skill',
      tags: ['云数据库', '同步'],
      usageCount: 0
    }
  ];

  /**
   * 测试1: BrickManager基础功能测试
   */
  async function testBrickManagerBasics() {
    console.log('\n🧪 测试1: BrickManager基础功能');
    
    try {
      const { instance: BrickManager } = require('utils/brick-manager.js');
      console.log('✅ BrickManager加载成功');
      
      // 测试初始化
      await BrickManager.init();
      console.log('✅ BrickManager初始化成功');
      
      // 测试获取当前积木
      const currentBricks = await BrickManager.getBricks();
      console.log(`📊 当前积木数量: ${currentBricks.length}`);
      
      return { success: true, currentCount: currentBricks.length };
    } catch (error) {
      console.error('❌ BrickManager基础功能测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试2: 数据持久化功能测试
   */
  async function testDataPersistence() {
    console.log('\n🧪 测试2: 数据持久化功能');
    
    try {
      const { instance: BrickManager } = require('utils/brick-manager.js');
      
      // 获取保存前的积木数量
      const beforeBricks = await BrickManager.getBricks();
      const beforeCount = beforeBricks.length;
      console.log(`📊 保存前积木数量: ${beforeCount}`);
      
      // 批量保存测试积木
      console.log('🔄 开始批量保存测试积木...');
      const saveResult = await BrickManager.addBricksBatch(testBricks);
      
      console.log('📊 保存结果:', {
        success: saveResult.success,
        totalCount: saveResult.totalCount,
        addedCount: saveResult.addedCount,
        cloudSaveSuccess: saveResult.cloudSaveSuccess
      });
      
      // 验证保存后的数据
      const afterBricks = await BrickManager.getBricks();
      const afterCount = afterBricks.length;
      console.log(`📊 保存后积木数量: ${afterCount}`);
      
      // 检查是否包含测试积木
      const hasTestBricks = testBricks.every(testBrick => 
        afterBricks.some(brick => brick.id === testBrick.id)
      );
      
      console.log(`🔍 包含所有测试积木: ${hasTestBricks ? '✅ 是' : '❌ 否'}`);
      
      const success = saveResult.success && hasTestBricks && (afterCount >= beforeCount + testBricks.length);
      console.log(`🎯 数据持久化测试: ${success ? '✅ 通过' : '❌ 失败'}`);
      
      return {
        success,
        beforeCount,
        afterCount,
        addedCount: saveResult.addedCount,
        hasTestBricks,
        cloudSaveSuccess: saveResult.cloudSaveSuccess
      };
      
    } catch (error) {
      console.error('❌ 数据持久化测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试3: 本地存储验证
   */
  async function testLocalStorage() {
    console.log('\n🧪 测试3: 本地存储验证');
    
    try {
      // 检查本地存储中的积木数据
      const localBricks = wx.getStorageSync('bricks');
      const localSyncTime = wx.getStorageSync('bricks_sync_time');
      
      console.log(`📊 本地存储积木数量: ${localBricks ? localBricks.length : 0}`);
      console.log(`🕒 最后同步时间: ${localSyncTime || '未设置'}`);
      
      // 检查是否包含测试积木
      const hasTestBricksInLocal = localBricks && testBricks.every(testBrick => 
        localBricks.some(brick => brick.id === testBrick.id)
      );
      
      console.log(`🔍 本地存储包含测试积木: ${hasTestBricksInLocal ? '✅ 是' : '❌ 否'}`);
      
      return {
        success: !!localBricks && hasTestBricksInLocal,
        localCount: localBricks ? localBricks.length : 0,
        hasTestBricks: hasTestBricksInLocal,
        syncTime: localSyncTime
      };
      
    } catch (error) {
      console.error('❌ 本地存储验证失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试4: 全局状态验证
   */
  async function testGlobalState() {
    console.log('\n🧪 测试4: 全局状态验证');
    
    try {
      const app = getApp();
      const globalBricks = app.globalData ? app.globalData.bricks : null;
      
      console.log(`📊 全局状态积木数量: ${globalBricks ? globalBricks.length : 0}`);
      
      // 检查是否包含测试积木
      const hasTestBricksInGlobal = globalBricks && testBricks.every(testBrick => 
        globalBricks.some(brick => brick.id === testBrick.id)
      );
      
      console.log(`🔍 全局状态包含测试积木: ${hasTestBricksInGlobal ? '✅ 是' : '❌ 否'}`);
      
      return {
        success: !!globalBricks && hasTestBricksInGlobal,
        globalCount: globalBricks ? globalBricks.length : 0,
        hasTestBricks: hasTestBricksInGlobal
      };
      
    } catch (error) {
      console.error('❌ 全局状态验证失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试5: 页面切换模拟测试
   */
  async function testPageSwitching() {
    console.log('\n🧪 测试5: 页面切换模拟测试');
    
    try {
      const { instance: BrickManager } = require('utils/brick-manager.js');
      
      // 模拟页面切换前的数据获取
      console.log('📱 模拟从积木库页面获取数据...');
      const bricksFromBricksPage = await BrickManager.getBricks();
      console.log(`📊 积木库页面积木数量: ${bricksFromBricksPage.length}`);
      
      // 模拟页面切换 - 强制重新初始化BrickManager
      console.log('🔄 模拟页面切换，重新初始化BrickManager...');
      BrickManager.initialized = false;
      BrickManager.bricks = [];
      
      // 模拟切换到生成页面后重新获取数据
      console.log('📱 模拟切换到生成页面，重新获取数据...');
      const bricksFromGeneratePage = await BrickManager.getBricks();
      console.log(`📊 生成页面积木数量: ${bricksFromGeneratePage.length}`);
      
      // 验证数据一致性
      const dataConsistent = bricksFromBricksPage.length === bricksFromGeneratePage.length;
      console.log(`🔍 页面切换数据一致性: ${dataConsistent ? '✅ 一致' : '❌ 不一致'}`);
      
      // 验证测试积木是否仍然存在
      const hasTestBricksAfterSwitch = testBricks.every(testBrick => 
        bricksFromGeneratePage.some(brick => brick.id === testBrick.id)
      );
      
      console.log(`🔍 页面切换后测试积木存在: ${hasTestBricksAfterSwitch ? '✅ 是' : '❌ 否'}`);
      
      const success = dataConsistent && hasTestBricksAfterSwitch;
      console.log(`🎯 页面切换测试: ${success ? '✅ 通过' : '❌ 失败'}`);
      
      return {
        success,
        beforeSwitchCount: bricksFromBricksPage.length,
        afterSwitchCount: bricksFromGeneratePage.length,
        dataConsistent,
        hasTestBricks: hasTestBricksAfterSwitch
      };
      
    } catch (error) {
      console.error('❌ 页面切换测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 测试6: 刷新后数据持久性测试
   */
  async function testRefreshPersistence() {
    console.log('\n🧪 测试6: 刷新后数据持久性');
    
    try {
      const { instance: BrickManager } = require('utils/brick-manager.js');
      
      // 强制刷新BrickManager
      console.log('🔄 强制刷新BrickManager...');
      await BrickManager.refresh();
      
      const refreshedBricks = await BrickManager.getBricks();
      console.log(`📊 刷新后积木数量: ${refreshedBricks.length}`);
      
      // 验证测试积木是否仍然存在
      const hasTestBricksAfterRefresh = testBricks.every(testBrick => 
        refreshedBricks.some(brick => brick.id === testBrick.id)
      );
      
      console.log(`🔍 刷新后测试积木存在: ${hasTestBricksAfterRefresh ? '✅ 是' : '❌ 否'}`);
      
      const success = refreshedBricks.length > 0 && hasTestBricksAfterRefresh;
      console.log(`🎯 刷新持久性测试: ${success ? '✅ 通过' : '❌ 失败'}`);
      
      return {
        success,
        refreshedCount: refreshedBricks.length,
        hasTestBricks: hasTestBricksAfterRefresh
      };
      
    } catch (error) {
      console.error('❌ 刷新持久性测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 运行完整的综合测试
   */
  async function runComprehensiveTest() {
    console.log('🚀 开始运行综合数据持久化和页面切换测试...');
    console.log('=' .repeat(60));
    
    const results = {
      timestamp: new Date().toISOString(),
      tests: {}
    };
    
    try {
      // 运行所有测试
      results.tests.basics = await testBrickManagerBasics();
      results.tests.persistence = await testDataPersistence();
      results.tests.localStorage = await testLocalStorage();
      results.tests.globalState = await testGlobalState();
      results.tests.pageSwitching = await testPageSwitching();
      results.tests.refreshPersistence = await testRefreshPersistence();
      
      // 计算总体成功率
      const testCount = Object.keys(results.tests).length;
      const successCount = Object.values(results.tests).filter(test => test.success).length;
      results.successRate = (successCount / testCount) * 100;
      results.overall = results.successRate >= 95 ? 'PASS' : 'FAIL';
      
      console.log('\n' + '=' .repeat(60));
      console.log('📋 综合测试完成');
      console.log(`🎯 总体结果: ${results.overall}`);
      console.log(`📊 成功率: ${results.successRate}%`);
      console.log(`✅ 通过测试: ${successCount}/${testCount}`);
      
      // 详细结果
      console.log('\n📋 详细测试结果:');
      Object.entries(results.tests).forEach(([testName, result], index) => {
        const status = result.success ? '✅' : '❌';
        console.log(`${index + 1}. ${status} ${testName}: ${result.success ? '通过' : '失败'}`);
        if (!result.success && result.error) {
          console.log(`   错误: ${result.error}`);
        }
      });
      
      if (results.overall === 'PASS') {
        console.log('\n🎉 恭喜！数据持久化和页面切换功能完全正常！');
        console.log('✅ BrickManager能正确保存数据到云数据库和本地存储');
        console.log('✅ 数据在页面切换后仍然保持');
        console.log('✅ 刷新后数据持久存在');
        console.log('✅ 所有存储层面数据一致');
      } else {
        console.log('\n⚠️ 部分功能存在问题，需要进一步修复');
      }
      
      return results;
      
    } catch (error) {
      console.error('❌ 综合测试运行失败:', error);
      results.error = error.message;
      results.overall = 'ERROR';
      return results;
    }
  }

  // 自动运行测试
  console.log('🚀 自动开始综合测试...\n');
  runComprehensiveTest().then(result => {
    console.log('\n🏁 综合测试完成！');
    if (result.overall === 'PASS') {
      console.log('🎊 所有功能验证成功！数据持久化问题已彻底解决。');
    } else {
      console.log('🔧 仍有部分问题需要解决，请查看详细测试结果。');
    }
  }).catch(error => {
    console.error('💥 测试运行异常:', error);
  });

  // 全局暴露测试函数
  if (typeof global !== 'undefined') {
    global.runComprehensiveTest = runComprehensiveTest;
    global.testBrickManagerBasics = testBrickManagerBasics;
    global.testDataPersistence = testDataPersistence;
    global.testPageSwitching = testPageSwitching;
  }
  if (typeof window !== 'undefined') {
    window.runComprehensiveTest = runComprehensiveTest;
    window.testBrickManagerBasics = testBrickManagerBasics;
    window.testDataPersistence = testDataPersistence;
    window.testPageSwitching = testPageSwitching;
  }
}

console.log('\n📖 可用的测试函数:');
console.log('- runComprehensiveTest() // 运行完整综合测试');
console.log('- testBrickManagerBasics() // 测试基础功能');
console.log('- testDataPersistence() // 测试数据持久化');
console.log('- testPageSwitching() // 测试页面切换');
console.log('\n⚠️ 重要: 此测试使用真实数据，不使用任何模拟数据');
