// 云存储权限修复测试脚本
// 在微信开发者工具的Console中运行此脚本

console.log('🔧 开始测试云存储权限修复效果...');

// 1. 检查云开发初始化状态
function checkCloudInit() {
  console.log('📡 检查云开发初始化状态...');
  
  if (!wx.cloud) {
    console.error('❌ wx.cloud 不可用，请检查基础库版本');
    return false;
  }
  
  console.log('✅ wx.cloud 可用');
  return true;
}

// 2. 检查用户登录状态
async function checkLoginStatus() {
  console.log('👤 检查用户登录状态...');
  
  try {
    // 检查本地存储的登录信息
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    console.log('本地存储状态:', {
      hasToken: !!token,
      hasUserInfo: !!userInfo
    });
    
    // 检查全局登录状态
    const app = getApp();
    console.log('全局登录状态:', {
      isLoggedIn: app.globalData.isLoggedIn,
      hasUserInfo: !!app.globalData.userInfo
    });
    
    return !!token || app.globalData.isLoggedIn;
    
  } catch (error) {
    console.error('❌ 检查登录状态失败:', error);
    return false;
  }
}

// 3. 测试文件上传权限
async function testUploadPermission() {
  console.log('📤 测试文件上传权限...');
  
  try {
    // 创建一个测试文件
    const testContent = 'Test file for upload permission check';
    const testPath = `test-uploads/permission-test-${Date.now()}.txt`;
    
    // 尝试上传测试文件
    const result = await wx.cloud.uploadFile({
      cloudPath: testPath,
      filePath: 'data:text/plain;base64,' + btoa(testContent)
    });
    
    console.log('✅ 文件上传成功!', result);
    
    // 清理测试文件
    try {
      await wx.cloud.deleteFile({
        fileList: [result.fileID]
      });
      console.log('🗑️ 测试文件已清理');
    } catch (cleanupError) {
      console.warn('⚠️ 清理测试文件失败:', cleanupError);
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 文件上传失败:', error);
    
    if (error.errCode === -503002) {
      console.error('🚨 仍然存在权限问题，请检查：');
      console.error('1. 用户是否已登录');
      console.error('2. 云存储安全规则是否正确设置');
    }
    
    return false;
  }
}

// 4. 主测试函数
async function runUploadTest() {
  console.log('🚀 开始云存储权限修复测试...');
  
  // 检查云开发初始化
  if (!checkCloudInit()) {
    return;
  }
  
  // 检查登录状态
  const isLoggedIn = await checkLoginStatus();
  if (!isLoggedIn) {
    console.warn('⚠️ 用户未登录，这可能导致上传失败');
    console.log('💡 建议先进行微信登录');
  }
  
  // 测试上传权限
  const uploadSuccess = await testUploadPermission();
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log('- 云开发初始化: ✅');
  console.log('- 用户登录状态:', isLoggedIn ? '✅' : '❌');
  console.log('- 文件上传权限:', uploadSuccess ? '✅' : '❌');
  
  if (uploadSuccess) {
    console.log('\n🎉 云存储权限修复成功！现在可以正常上传文件了。');
  } else {
    console.log('\n🔧 需要进一步排查问题：');
    if (!isLoggedIn) {
      console.log('1. 请先进行微信登录');
    }
    console.log('2. 检查网络连接');
    console.log('3. 查看详细错误信息');
  }
}

// 执行测试
runUploadTest().catch(error => {
  console.error('❌ 测试执行失败:', error);
});
