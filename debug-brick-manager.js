/**
 * 积木管理器调试脚本
 * 用于测试和调试云函数调用问题
 */

// 测试云函数调用
async function testBrickManagerCloudFunction() {
  console.log('🧪 开始测试积木管理器云函数调用...');
  
  try {
    // 获取应用实例
    const app = getApp();
    if (!app.brickManager) {
      console.error('❌ BrickManager未初始化');
      return;
    }

    console.log('📋 测试1: 加载积木数据');
    try {
      const bricks = await app.brickManager.loadFromCloudDatabase();
      console.log('✅ 加载积木数据成功:', bricks);
    } catch (error) {
      console.error('❌ 加载积木数据失败:', error);
    }

    console.log('📋 测试2: 添加测试积木');
    try {
      const testBrick = {
        title: '测试积木',
        content: '这是一个测试积木，用于验证云函数调用',
        category: '技术能力',
        level: '熟练',
        tags: ['测试', '调试'],
        confidence: 0.9
      };
      
      const result = await app.brickManager.addBrick(testBrick);
      console.log('✅ 添加积木成功:', result);
    } catch (error) {
      console.error('❌ 添加积木失败:', error);
    }

    console.log('📋 测试3: 获取积木统计');
    try {
      const stats = app.brickManager.getStatistics();
      console.log('✅ 获取统计成功:', stats);
    } catch (error) {
      console.error('❌ 获取统计失败:', error);
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 直接测试云函数调用
async function testDirectCloudFunction() {
  console.log('🧪 开始直接测试云函数调用...');
  
  try {
    console.log('📋 直接调用brickManager云函数...');
    
    const result = await wx.cloud.callFunction({
      name: 'brickManager',
      data: {
        action: 'list',
        data: {
          limit: 10
        }
      }
    });

    console.log('🔍 云函数原始返回结果:', {
      errMsg: result.errMsg,
      requestID: result.requestID,
      result: result.result
    });

    // 详细分析返回结果
    if (result.result) {
      console.log('📦 result.result 类型:', typeof result.result);
      console.log('📦 result.result 内容:', result.result);
      
      if (result.result.statusCode) {
        console.log('📦 检测到statusCode:', result.result.statusCode);
        if (result.result.body) {
          console.log('📦 body类型:', typeof result.result.body);
          console.log('📦 body内容:', result.result.body);
          
          if (typeof result.result.body === 'string') {
            try {
              const parsedBody = JSON.parse(result.result.body);
              console.log('📦 解析后的body:', parsedBody);
            } catch (e) {
              console.error('❌ 无法解析body为JSON:', e);
            }
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ 直接云函数调用失败:', error);
    console.error('❌ 错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
  }
}

// 测试用户身份验证
async function testUserAuth() {
  console.log('🧪 开始测试用户身份验证...');
  
  try {
    // 检查登录状态
    const app = getApp();
    console.log('📋 全局数据:', {
      isLoggedIn: app.globalData.isLoggedIn,
      userInfo: app.globalData.userInfo,
      isCloudConnected: app.globalData.isCloudConnected
    });

    // 检查本地存储
    const userInfo = wx.getStorageSync('userInfo');
    const isLoggedIn = wx.getStorageSync('isLoggedIn');
    const openid = wx.getStorageSync('openid');
    
    console.log('📋 本地存储:', {
      userInfo: userInfo,
      isLoggedIn: isLoggedIn,
      openid: openid
    });

    // 测试云开发身份
    if (wx.cloud) {
      try {
        const cloudUser = await wx.cloud.callFunction({
          name: 'ping',
          data: {}
        });
        console.log('📋 云开发身份测试:', cloudUser);
      } catch (error) {
        console.error('❌ 云开发身份测试失败:', error);
      }
    }

  } catch (error) {
    console.error('❌ 用户身份验证测试失败:', error);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行所有调试测试...');
  
  await testUserAuth();
  await testDirectCloudFunction();
  await testBrickManagerCloudFunction();
  
  console.log('✅ 所有测试完成');
}

// 导出测试函数
module.exports = {
  testBrickManagerCloudFunction,
  testDirectCloudFunction,
  testUserAuth,
  runAllTests
};

// 如果在页面中使用，可以直接调用
if (typeof Page !== 'undefined') {
  // 在页面中可以这样使用：
  // const debugScript = require('../../debug-brick-manager.js');
  // debugScript.runAllTests();
}
