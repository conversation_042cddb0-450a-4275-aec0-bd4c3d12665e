/**
 * 页面切换数据持久性测试
 * 模拟用户在不同tab页面之间切换，验证积木数据的持久性
 * 必须在微信小程序环境中运行
 */

console.log('📱 页面切换数据持久性测试');
console.log('🔍 环境检查:', typeof wx !== 'undefined' ? '✅ 微信小程序' : '❌ 非微信环境');

if (typeof wx === 'undefined') {
  console.error('❌ 请在微信开发者工具控制台中运行此测试');
} else {
  
  /**
   * 模拟页面切换的工具函数
   */
  class PageSwitchSimulator {
    constructor() {
      this.currentPage = null;
      this.testResults = [];
    }

    /**
     * 模拟切换到指定页面
     */
    async switchToPage(pagePath) {
      console.log(`📱 模拟切换到页面: ${pagePath}`);
      
      try {
        // 记录切换前的时间
        const startTime = Date.now();
        
        // 模拟页面切换 - 使用wx.switchTab
        await new Promise((resolve, reject) => {
          wx.switchTab({
            url: `/${pagePath}`,
            success: () => {
              const endTime = Date.now();
              console.log(`✅ 页面切换成功，耗时: ${endTime - startTime}ms`);
              this.currentPage = pagePath;
              resolve();
            },
            fail: (error) => {
              console.error(`❌ 页面切换失败:`, error);
              reject(error);
            }
          });
        });
        
        // 等待页面加载完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return { success: true, page: pagePath };
      } catch (error) {
        console.error(`❌ 切换到页面 ${pagePath} 失败:`, error);
        return { success: false, page: pagePath, error: error.message };
      }
    }

    /**
     * 在指定页面获取积木数据
     */
    async getBricksOnPage(pagePath) {
      console.log(`📊 在页面 ${pagePath} 获取积木数据`);
      
      try {
        const { instance: BrickManager } = require('utils/brick-manager.js');
        
        // 获取积木数据
        const bricks = await BrickManager.getBricks();
        console.log(`📊 页面 ${pagePath} 积木数量: ${bricks.length}`);
        
        // 检查数据完整性
        const hasValidData = bricks.length > 0 && bricks.every(brick => 
          brick.id && brick.title && brick.content
        );
        
        console.log(`🔍 页面 ${pagePath} 数据完整性: ${hasValidData ? '✅ 完整' : '❌ 不完整'}`);
        
        return {
          success: true,
          page: pagePath,
          count: bricks.length,
          hasValidData,
          bricks: bricks.map(b => ({ id: b.id, title: b.title }))
        };
        
      } catch (error) {
        console.error(`❌ 在页面 ${pagePath} 获取积木数据失败:`, error);
        return {
          success: false,
          page: pagePath,
          error: error.message
        };
      }
    }

    /**
     * 验证数据一致性
     */
    verifyDataConsistency(results) {
      console.log('🔍 验证页面间数据一致性...');
      
      if (results.length < 2) {
        console.log('⚠️ 需要至少2个页面的数据才能验证一致性');
        return false;
      }
      
      const firstPageCount = results[0].count;
      const allCountsMatch = results.every(result => result.count === firstPageCount);
      
      console.log(`📊 所有页面积木数量一致: ${allCountsMatch ? '✅ 是' : '❌ 否'}`);
      
      // 检查积木ID一致性
      const firstPageBrickIds = new Set(results[0].bricks.map(b => b.id));
      const allIdsMatch = results.every(result => {
        const currentIds = new Set(result.bricks.map(b => b.id));
        return firstPageBrickIds.size === currentIds.size && 
               [...firstPageBrickIds].every(id => currentIds.has(id));
      });
      
      console.log(`🔍 所有页面积木ID一致: ${allIdsMatch ? '✅ 是' : '❌ 否'}`);
      
      return allCountsMatch && allIdsMatch;
    }
  }

  /**
   * 运行完整的页面切换测试
   */
  async function runPageSwitchingTest() {
    console.log('🚀 开始页面切换数据持久性测试...');
    console.log('=' .repeat(50));
    
    const simulator = new PageSwitchSimulator();
    const testPages = [
      'pages/generate/generate',
      'pages/bricks/bricks',
      'pages/profile/profile'
    ];
    
    const results = {
      timestamp: new Date().toISOString(),
      pageResults: [],
      switchResults: [],
      dataConsistency: false,
      overall: 'FAIL'
    };
    
    try {
      console.log('📱 测试页面列表:', testPages);
      
      // 第一轮：依次切换到每个页面并获取数据
      console.log('\n🔄 第一轮：依次切换页面并获取数据');
      for (const pagePath of testPages) {
        // 切换到页面
        const switchResult = await simulator.switchToPage(pagePath);
        results.switchResults.push(switchResult);
        
        if (switchResult.success) {
          // 获取页面数据
          const pageData = await simulator.getBricksOnPage(pagePath);
          results.pageResults.push(pageData);
        } else {
          console.error(`❌ 无法切换到页面 ${pagePath}，跳过数据获取`);
        }
        
        // 等待一段时间再切换下一个页面
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      // 验证数据一致性
      console.log('\n🔍 验证数据一致性');
      const validResults = results.pageResults.filter(r => r.success && r.hasValidData);
      results.dataConsistency = simulator.verifyDataConsistency(validResults);
      
      // 第二轮：再次切换验证数据持久性
      console.log('\n🔄 第二轮：再次切换验证数据持久性');
      const secondRoundResults = [];
      
      for (const pagePath of testPages) {
        await simulator.switchToPage(pagePath);
        const pageData = await simulator.getBricksOnPage(pagePath);
        secondRoundResults.push(pageData);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // 比较两轮结果
      console.log('\n📊 比较两轮测试结果');
      const firstRoundCounts = validResults.map(r => r.count);
      const secondRoundCounts = secondRoundResults.filter(r => r.success).map(r => r.count);
      
      const persistenceTest = firstRoundCounts.length === secondRoundCounts.length &&
                             firstRoundCounts.every((count, index) => count === secondRoundCounts[index]);
      
      console.log(`🔍 数据持久性测试: ${persistenceTest ? '✅ 通过' : '❌ 失败'}`);
      
      // 计算总体结果
      const successfulSwitches = results.switchResults.filter(r => r.success).length;
      const successfulDataGets = results.pageResults.filter(r => r.success && r.hasValidData).length;
      const switchSuccessRate = (successfulSwitches / results.switchResults.length) * 100;
      const dataSuccessRate = (successfulDataGets / results.pageResults.length) * 100;
      
      results.overall = (switchSuccessRate >= 95 && dataSuccessRate >= 95 && 
                        results.dataConsistency && persistenceTest) ? 'PASS' : 'FAIL';
      
      console.log('\n' + '=' .repeat(50));
      console.log('📋 页面切换测试结果汇总');
      console.log(`🎯 总体结果: ${results.overall}`);
      console.log(`📱 页面切换成功率: ${switchSuccessRate}%`);
      console.log(`📊 数据获取成功率: ${dataSuccessRate}%`);
      console.log(`🔍 数据一致性: ${results.dataConsistency ? '✅ 通过' : '❌ 失败'}`);
      console.log(`💾 数据持久性: ${persistenceTest ? '✅ 通过' : '❌ 失败'}`);
      
      // 详细结果
      console.log('\n📋 详细页面测试结果:');
      results.pageResults.forEach((result, index) => {
        const status = result.success && result.hasValidData ? '✅' : '❌';
        console.log(`${index + 1}. ${status} ${result.page}: ${result.count}个积木`);
      });
      
      if (results.overall === 'PASS') {
        console.log('\n🎉 恭喜！页面切换数据持久性测试完全通过！');
        console.log('✅ 所有页面切换正常');
        console.log('✅ 数据在页面间保持一致');
        console.log('✅ 数据在多次切换后仍然持久');
        console.log('✅ 积木数据在页面切换时不会丢失');
      } else {
        console.log('\n⚠️ 页面切换测试存在问题，需要进一步修复');
      }
      
      return results;
      
    } catch (error) {
      console.error('❌ 页面切换测试运行失败:', error);
      results.error = error.message;
      results.overall = 'ERROR';
      return results;
    }
  }

  /**
   * 快速页面切换测试
   */
  async function quickPageSwitchTest() {
    console.log('\n🚀 快速页面切换测试');
    
    try {
      const { instance: BrickManager } = require('utils/brick-manager.js');
      
      // 获取初始数据
      const initialBricks = await BrickManager.getBricks();
      console.log(`📊 初始积木数量: ${initialBricks.length}`);
      
      // 快速切换页面
      const pages = ['pages/generate/generate', 'pages/bricks/bricks', 'pages/profile/profile'];
      
      for (let i = 0; i < 3; i++) {
        console.log(`🔄 第${i + 1}轮快速切换`);
        
        for (const page of pages) {
          await new Promise((resolve) => {
            wx.switchTab({
              url: `/${page}`,
              success: resolve,
              fail: resolve
            });
          });
          
          // 短暂等待
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // 获取数据
          const bricks = await BrickManager.getBricks();
          console.log(`  📊 ${page}: ${bricks.length}个积木`);
        }
      }
      
      // 最终验证
      const finalBricks = await BrickManager.getBricks();
      const dataStable = finalBricks.length === initialBricks.length;
      
      console.log(`🎯 快速切换测试: ${dataStable ? '✅ 通过' : '❌ 失败'}`);
      console.log(`📊 最终积木数量: ${finalBricks.length} (初始: ${initialBricks.length})`);
      
      return { success: dataStable, initialCount: initialBricks.length, finalCount: finalBricks.length };
      
    } catch (error) {
      console.error('❌ 快速页面切换测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 自动运行测试
  console.log('🚀 自动开始页面切换测试...\n');
  runPageSwitchingTest().then(result => {
    console.log('\n🏁 页面切换测试完成！');
    if (result.overall === 'PASS') {
      console.log('🎊 页面切换数据持久性验证成功！');
    } else {
      console.log('🔧 页面切换存在问题，请查看详细结果。');
    }
    
    // 运行快速切换测试
    return quickPageSwitchTest();
  }).then(quickResult => {
    console.log('\n📱 快速切换测试结果:', quickResult.success ? '✅ 通过' : '❌ 失败');
  }).catch(error => {
    console.error('💥 测试运行异常:', error);
  });

  // 全局暴露测试函数
  if (typeof global !== 'undefined') {
    global.runPageSwitchingTest = runPageSwitchingTest;
    global.quickPageSwitchTest = quickPageSwitchTest;
  }
  if (typeof window !== 'undefined') {
    window.runPageSwitchingTest = runPageSwitchingTest;
    window.quickPageSwitchTest = quickPageSwitchTest;
  }
}

console.log('\n📖 可用的页面切换测试函数:');
console.log('- runPageSwitchingTest() // 完整页面切换测试');
console.log('- quickPageSwitchTest() // 快速页面切换测试');
console.log('\n⚠️ 重要: 此测试会实际切换页面，请在微信开发者工具中运行');
