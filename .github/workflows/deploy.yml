name: Deploy to WeChat CloudRun

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        # 安装根目录依赖（如果存在）
        if [ -f "package.json" ]; then
          npm ci || npm install
        fi

        # 安装云托管服务依赖
        cd cloud-run/resume-snapshot
        npm ci || npm install
        cd ../..

    - name: Run tests
      run: |
        npm run lint || echo "No lint script found"
        npm test || echo "No test script found"
        
    - name: Install WeChat CloudRun CLI
      run: |
        npm install -g @cloudbase/cli@latest
        
    - name: Deploy to WeChat CloudRun
      env:
        CLOUDBASE_SECRET_ID: ${{ secrets.CLOUDBASE_SECRET_ID }}
        CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
        CLOUDBASE_ENV_ID: ${{ secrets.CLOUDBASE_ENV_ID }}
      run: |
        echo "🚀 开始部署到微信云托管"
        echo "📋 环境信息:"
        echo "  - 环境ID: $CLOUDBASE_ENV_ID"
        echo "  - 服务名称: resume-snapshot"
        echo "  - 目标目录: ./cloud-run/resume-snapshot"

        # 登录CloudBase CLI
        echo "🔐 登录CloudBase CLI..."
        npx @cloudbase/cli login --key

        # 验证登录状态
        npx @cloudbase/cli env:list

        # 部署云托管服务
        echo "📦 开始部署云托管服务..."
        npx @cloudbase/cli framework deploy --envId $CLOUDBASE_ENV_ID --verbose

        echo "✅ 部署命令执行完成"
        
    - name: Health Check
      env:
        CLOUDBASE_ENV_ID: ${{ secrets.CLOUDBASE_ENV_ID }}
      run: |
        echo "⏳ 等待服务启动..."
        sleep 60

        # 尝试从CloudBase CLI获取服务信息
        echo "🔍 获取服务访问地址..."

        # 构建可能的服务URL（基于环境ID和服务名称）
        # 格式：https://resume-snapshot-{envId}.sh.run.tcloudbase.com
        SERVICE_URL="https://resume-snapshot-${CLOUDBASE_ENV_ID}.sh.run.tcloudbase.com"
        HEALTH_URL="${SERVICE_URL}/health"

        echo "🏥 检查服务健康状态: $HEALTH_URL"

        for i in {1..10}; do
          echo "📡 尝试连接 (第 $i/10 次)..."
          if curl -f -s -m 10 "$HEALTH_URL"; then
            echo "✅ 服务健康检查通过！"
            echo "🌐 服务地址: $SERVICE_URL"
            break
          else
            echo "⏳ 服务尚未就绪，等待30秒后重试..."
            sleep 30
          fi

          if [ $i -eq 10 ]; then
            echo "⚠️ 健康检查超时，但部署可能仍然成功"
            echo "📝 请手动检查微信云托管控制台中的服务状态"
          fi
        done
        
    - name: Test Screenshot Service
      env:
        CLOUDBASE_ENV_ID: ${{ secrets.CLOUDBASE_ENV_ID }}
      run: |
        # 构建服务URL
        SERVICE_URL="https://resume-snapshot-${CLOUDBASE_ENV_ID}.sh.run.tcloudbase.com"
        SNAPSHOT_URL="${SERVICE_URL}/snapshot"

        echo "📸 测试截图服务: $SNAPSHOT_URL"

        # 测试截图功能
        curl -X POST "$SNAPSHOT_URL" \
          -H "Content-Type: application/json" \
          -d '{"url":"https://example.com","format":"png","width":800,"height":600}' \
          --max-time 60 \
          --output test-screenshot.png \
          --fail \
          --show-error || echo "⚠️ 截图服务测试失败，但不阻止部署完成"

        # 检查生成的文件
        if [ -f "test-screenshot.png" ]; then
          echo "✅ 截图文件生成成功"
          ls -la test-screenshot.png
        else
          echo "❌ 截图文件未生成"
        fi
          
    - name: Notify Deployment Result
      if: always()
      env:
        CLOUDBASE_ENV_ID: ${{ secrets.CLOUDBASE_ENV_ID }}
      run: |
        SERVICE_URL="https://resume-snapshot-${CLOUDBASE_ENV_ID}.sh.run.tcloudbase.com"

        if [ "${{ job.status }}" == "success" ]; then
          echo "🎉 部署成功！"
          echo "📱 微信云托管服务已更新"
          echo "🌐 服务地址: $SERVICE_URL"
          echo "🔗 健康检查: ${SERVICE_URL}/health"
          echo "📸 截图服务: ${SERVICE_URL}/snapshot"
          echo ""
          echo "📋 下一步操作："
          echo "1. 访问微信云托管控制台查看服务状态"
          echo "2. 更新云函数中的服务地址为: $SERVICE_URL"
          echo "3. 测试完整的简历生成流程"
        else
          echo "❌ 部署失败，请检查日志"
          echo "🔍 排查建议："
          echo "1. 检查GitHub Secrets配置是否正确"
          echo "2. 查看构建日志中的错误信息"
          echo "3. 确认cloudbaserc.json配置是否正确"
          echo "4. 验证Dockerfile和依赖是否完整"
        fi
