/**
 * 测试积木库显示逻辑修改效果
 * 在微信开发者工具Console中运行
 */

function testBrickDisplayLogic() {
  console.log('🔍 测试积木库显示逻辑修改效果...');
  
  try {
    // 1. 检查当前页面
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route !== 'pages/bricks/bricks') {
      console.log('⚠️ 请先导航到积木库页面 (pages/bricks/bricks)');
      return false;
    }
    
    // 2. 检查页面数据
    const bricks = currentPage.data.bricks || [];
    
    console.log('📊 当前积木库数据:');
    console.log('- 总积木数:', bricks.length);
    
    // 3. 分析各类积木的显示格式
    const personalBricks = bricks.filter(brick => brick.category === 'personal');
    const educationBricks = bricks.filter(brick => brick.category === 'education');
    const experienceBricks = bricks.filter(brick => brick.category === 'experience');
    
    console.log('\n👤 个人信息积木分析:');
    personalBricks.forEach((brick, index) => {
      console.log(`${index + 1}. 标题: "${brick.title}"`);
      console.log(`   描述: "${brick.description}"`);
      console.log(`   数据: ${JSON.stringify(brick.data, null, 2)}`);
      
      // 检查是否符合新的显示逻辑
      const isCorrectFormat = !brick.title.includes(' - 个人信息');
      console.log(`   ✅ 格式正确: ${isCorrectFormat ? '是' : '否'}`);
    });
    
    console.log('\n🎓 教育背景积木分析:');
    educationBricks.forEach((brick, index) => {
      console.log(`${index + 1}. 标题: "${brick.title}"`);
      console.log(`   描述: "${brick.description}"`);
      console.log(`   数据: ${JSON.stringify(brick.data, null, 2)}`);
      
      // 检查是否符合新的显示逻辑
      const isCorrectFormat = !brick.description.includes('学校：') && brick.description.includes('|');
      console.log(`   ✅ 格式正确: ${isCorrectFormat ? '是' : '否'}`);
    });
    
    console.log('\n💼 工作经历积木分析:');
    experienceBricks.forEach((brick, index) => {
      console.log(`${index + 1}. 标题: "${brick.title}"`);
      console.log(`   描述: "${brick.description}"`);
      console.log(`   数据: ${JSON.stringify(brick.data, null, 2)}`);
      
      // 检查是否符合新的显示逻辑
      const isCorrectFormat = !brick.title.includes(' - ') && brick.description.includes('|');
      console.log(`   ✅ 格式正确: ${isCorrectFormat ? '是' : '否'}`);
    });
    
    // 4. 总结修改效果
    console.log('\n📋 修改效果总结:');
    console.log(`- 个人信息积木: ${personalBricks.length}个`);
    console.log(`- 教育背景积木: ${educationBricks.length}个`);
    console.log(`- 工作经历积木: ${experienceBricks.length}个`);
    
    const totalCorrect = personalBricks.filter(b => !b.title.includes(' - 个人信息')).length +
                        educationBricks.filter(b => !b.description.includes('学校：') && b.description.includes('|')).length +
                        experienceBricks.filter(b => !b.title.includes(' - ') && b.description.includes('|')).length;
    
    const totalBricks = personalBricks.length + educationBricks.length + experienceBricks.length;
    
    console.log(`- 格式正确的积木: ${totalCorrect}/${totalBricks}`);
    console.log(`- 修改成功率: ${totalBricks > 0 ? Math.round(totalCorrect / totalBricks * 100) : 0}%`);
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    return false;
  }
}

// 运行测试
testBrickDisplayLogic();
