const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const puppeteer = require('puppeteer');

const app = express();
const PORT = process.env.PORT || 80;

// 安全中间件
app.use(helmet());
app.use(cors());

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试'
});
app.use(limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));

// 健康检查端点
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'resume-snapshot'
  });
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: 'AI简历系统截图服务',
    service: 'resume-snapshot',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      snapshot: '/resume-snapshot'
    }
  });
});

// 简历截图服务
app.post('/resume-snapshot', async (req, res) => {
  let browser = null;
  
  try {
    const { html, options = {} } = req.body;
    
    if (!html) {
      return res.status(400).json({ 
        error: '缺少HTML内容',
        code: 'MISSING_HTML'
      });
    }

    // 启动浏览器
    browser = await puppeteer.launch({
      headless: 'new',
      executablePath: process.env.PUPPETEER_EXECUTABLE_PATH,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();
    
    // 设置页面大小
    await page.setViewport({
      width: options.width || 1200,
      height: options.height || 1600,
      deviceScaleFactor: 2
    });

    // 设置HTML内容
    await page.setContent(html, {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // 生成截图
    const screenshot = await page.screenshot({
      type: options.format || 'png',
      fullPage: true,
      quality: options.quality || 90
    });

    // 设置响应头
    res.setHeader('Content-Type', `image/${options.format || 'png'}`);
    res.setHeader('Content-Disposition', 'attachment; filename="resume-snapshot.png"');
    
    res.send(screenshot);

  } catch (error) {
    console.error('截图生成失败:', error);
    res.status(500).json({
      error: '截图生成失败',
      message: error.message,
      code: 'SCREENSHOT_FAILED'
    });
  } finally {
    if (browser) {
      await browser.close();
    }
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    error: '内部服务器错误',
    message: process.env.NODE_ENV === 'development' ? error.message : '服务暂时不可用'
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: '接口不存在',
    path: req.path,
    method: req.method
  });
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 简历截图服务启动成功`);
  console.log(`📍 服务地址: http://0.0.0.0:${PORT}`);
  console.log(`🏥 健康检查: http://0.0.0.0:${PORT}/health`);
  console.log(`📸 截图服务: http://0.0.0.0:${PORT}/resume-snapshot`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭...');
  process.exit(0);
});
