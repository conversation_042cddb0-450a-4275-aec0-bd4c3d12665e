# AI 简历系统微信云托管截图服务

[![部署状态](https://github.com/xuyuzeamazon/ai-resume-cloudrun/workflows/微信云托管自动部署/badge.svg)](https://github.com/xuyuzeamazon/ai-resume-cloudrun/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

基于 Node.js + Puppeteer 的简历截图服务，专为微信云托管设计，支持 HTML 转 PNG/JPEG 图片。

## 🚀 功能特性

- ✅ **高质量截图**: 使用 Puppeteer 生成高清简历截图
- ✅ **多格式支持**: 支持 PNG、JPEG 格式输出
- ✅ **云原生部署**: 专为微信云托管优化
- ✅ **自动化 CI/CD**: GitHub Actions 自动构建部署
- ✅ **安全防护**: 内置限流、CORS、安全头等防护
- ✅ **健康监控**: 完整的健康检查和监控端点
- ✅ **中文字体**: 内置中文字体支持

## 📋 API 接口

### 健康检查
```http
GET /health
```

### 生成简历截图
```http
POST /resume-snapshot
Content-Type: application/json

{
  "html": "<html>...</html>",
  "options": {
    "width": 1200,
    "height": 1600,
    "format": "png",
    "quality": 90,
    "fullPage": true
  }
}
```

## 🛠️ 本地开发

### 环境要求
- Node.js >= 18.0.0
- Docker (可选)

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 运行测试
```bash
npm test
```

### Docker 构建
```bash
# 构建镜像
npm run docker:build

# 运行容器
npm run docker:run
```

## 🚀 部署指南

### 1. 配置 GitHub Secrets

在 GitHub 仓库设置中添加以下 Secrets：

| Secret 名称 | 描述 | 获取方式 |
|------------|------|---------|
| `CLOUDBASE_ENV_ID` | 云开发环境 ID | 云开发控制台 |
| `CLOUDBASE_SECRET_ID` | 腾讯云 API 密钥 ID | 腾讯云控制台 > 访问管理 > API密钥管理 |
| `CLOUDBASE_SECRET_KEY` | 腾讯云 API 密钥 Key | 腾讯云控制台 > 访问管理 > API密钥管理 |

### 2. 自动部署

推送代码到 `main` 分支即可触发自动部署：

```bash
git add .
git commit -m "feat: 更新功能"
git push origin main
```

### 3. 手动部署

```bash
# 安装 CloudBase CLI
npm install -g @cloudbase/cli

# 登录
tcb login --apiKeyId YOUR_SECRET_ID --apiKey YOUR_SECRET_KEY

# 部署
tcb framework deploy --envId YOUR_ENV_ID
```

## 📁 项目结构

```
ai-resume-cloudrun/
├── .github/
│   └── workflows/
│       └── deploy.yml          # GitHub Actions 工作流
├── index.js                    # 主应用文件
├── package.json               # 项目配置
├── Dockerfile                 # Docker 配置
├── cloudbaserc.json          # CloudBase 配置
├── .gitignore                 # Git 忽略文件
└── README.md                  # 项目文档
```

## 🔧 环境变量

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `PORT` | `80` | 服务端口 |
| `NODE_ENV` | `production` | 运行环境 |
| `RATE_LIMIT_MAX` | `100` | 限流最大请求数 |
| `ALLOWED_ORIGINS` | `*` | 允许的跨域来源 |

## 🐛 故障排除

### 常见问题

1. **字体显示问题**
   - 确保 Dockerfile 中已安装中文字体包
   - 检查 HTML 中的字体设置

2. **内存不足**
   - 调整云托管实例规格
   - 优化 Puppeteer 启动参数

3. **部署失败**
   - 检查 GitHub Secrets 配置
   - 查看 Actions 日志详情

### 日志查看

```bash
# 查看服务日志
tcb functions:log --envId YOUR_ENV_ID --name resume-snapshot
```

## 📊 性能优化

- 使用 `--single-process` 减少内存占用
- 启用 gzip 压缩减少传输大小
- 实现连接池复用浏览器实例
- 设置合理的超时时间

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [微信云托管文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloudrun/)
- [CloudBase 文档](https://docs.cloudbase.net/)
- [Puppeteer 文档](https://pptr.dev/)

## 📞 支持

如有问题，请提交 [Issue](https://github.com/xuyuzeamazon/ai-resume-cloudrun/issues) 或联系维护者。
