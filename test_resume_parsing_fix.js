/**
 * 测试简历解析修复效果
 * 验证积木数据是否能正确从云函数传递到前端
 */

const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

async function testResumeParsingFix() {
  try {
    console.log('🧪 开始测试简历解析修复效果...\n');
    
    // 测试数据
    const testResumeContent = `
徐瑜泽
电话：13928303116
邮箱：<EMAIL>
地址：上海
职位：商家成长与赋能专家

教育背景：
2018-2022 北京大学 计算机科学与技术 本科

工作经历：
2022-2024 腾讯科技 前端工程师
- 负责微信小程序开发
- 参与云开发项目架构设计

技能：
- JavaScript
- React
- 微信小程序开发
- 云开发
    `;

    // 步骤1: 提交任务
    console.log('📤 步骤1: 提交简历解析任务...');
    const submitResult = await app.callFunction({
      name: 'resumeTaskSubmitter',
      data: {
        fileName: '测试简历-修复验证.txt',
        fileType: 'txt',
        resumeContent: testResumeContent,
        userId: 'test_fix_user'
      }
    });

    if (!submitResult.result || !submitResult.result.body) {
      throw new Error('任务提交失败');
    }

    const submitResponse = JSON.parse(submitResult.result.body);
    if (!submitResponse.success) {
      throw new Error(submitResponse.error || '任务提交失败');
    }

    const taskId = submitResponse.data.taskId;
    console.log('✅ 任务提交成功，taskId:', taskId);

    // 步骤2: 轮询查询任务状态
    console.log('\n🔍 步骤2: 轮询查询任务状态...');
    let attempts = 0;
    const maxAttempts = 20;
    const pollingInterval = 5000; // 5秒

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, pollingInterval));
      attempts++;

      console.log(`🔄 第${attempts}次查询...`);

      const queryResult = await app.callFunction({
        name: 'resumeTaskQuery',
        data: { taskId, userId: 'test_fix_user' }
      });

      if (!queryResult.result || !queryResult.result.body) {
        console.warn('⚠️ 查询失败，继续重试...');
        continue;
      }

      const queryResponse = JSON.parse(queryResult.result.body);
      if (!queryResponse.success) {
        console.warn('⚠️ 查询响应失败:', queryResponse.error);
        continue;
      }

      const taskStatus = queryResponse.data.data;
      console.log(`📊 任务状态: ${taskStatus.status}`);

      if (taskStatus.progress) {
        console.log(`📈 进度: ${taskStatus.progress}`);
      }

      // 检查任务状态
      switch (taskStatus.status) {
        case 'completed':
          console.log('\n✅ 任务完成!');
          
          // 步骤3: 验证积木数据
          console.log('🔍 步骤3: 验证积木数据结构...');
          
          const result = taskStatus.result;
          console.log('📊 结果数据结构检查:');
          console.log('- result存在:', !!result);
          console.log('- result.data存在:', !!result?.data);
          console.log('- result.data.bricks存在:', !!result?.data?.bricks);
          console.log('- result.data.bricks是数组:', Array.isArray(result?.data?.bricks));
          console.log('- 积木数量:', result?.data?.bricks?.length || 0);
          
          if (result?.data?.bricks && Array.isArray(result.data.bricks)) {
            const bricks = result.data.bricks;
            console.log('\n🧱 积木详细信息:');
            console.log(`总计: ${bricks.length} 个积木`);
            
            // 按分类统计
            const categories = {};
            bricks.forEach(brick => {
              const category = brick.category || 'unknown';
              categories[category] = (categories[category] || 0) + 1;
            });
            
            console.log('📊 分类统计:');
            Object.entries(categories).forEach(([category, count]) => {
              console.log(`- ${category}: ${count}个`);
            });
            
            // 显示前3个积木的详细信息
            console.log('\n📝 前3个积木详情:');
            bricks.slice(0, 3).forEach((brick, index) => {
              console.log(`${index + 1}. ${brick.title} (${brick.category})`);
              console.log(`   描述: ${brick.description?.substring(0, 100)}...`);
            });
            
            console.log('\n🎉 测试成功! 积木数据结构正确，前端应该能正确接收。');
            
          } else {
            console.error('\n❌ 测试失败! 积木数据不存在或格式错误');
            console.log('完整结果数据:', JSON.stringify(result, null, 2));
          }
          
          return;

        case 'failed':
          console.error('\n❌ 任务失败:', taskStatus.error);
          return;

        case 'pending':
          console.log('⏳ 任务排队中...');
          break;

        case 'processing':
          console.log('⚙️ 任务处理中...');
          break;

        default:
          console.log(`❓ 未知状态: ${taskStatus.status}`);
      }
    }

    console.error('\n⏰ 轮询超时，任务可能仍在处理中');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    console.error('错误详情:', error.message);
  }
}

// 运行测试
testResumeParsingFix().then(() => {
  console.log('\n🏁 测试完成');
}).catch(error => {
  console.error('\n💥 测试异常:', error);
});
