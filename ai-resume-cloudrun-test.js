const request = require('supertest');
const express = require('express');

// 模拟应用（简化版，用于测试）
const createTestApp = () => {
  const app = express();
  app.use(express.json());
  
  app.get('/health', (req, res) => {
    res.json({ 
      status: 'healthy', 
      timestamp: new Date().toISOString(),
      service: 'resume-snapshot'
    });
  });
  
  app.get('/', (req, res) => {
    res.json({
      message: 'AI简历系统截图服务',
      service: 'resume-snapshot',
      version: '1.0.0'
    });
  });
  
  app.get('/ping', (req, res) => {
    res.json({ message: 'pong' });
  });
  
  app.post('/resume-snapshot', (req, res) => {
    const { html } = req.body;
    if (!html) {
      return res.status(400).json({ error: '缺少HTML内容' });
    }
    // 模拟返回图片数据
    res.setHeader('Content-Type', 'image/png');
    res.send(Buffer.from('fake-image-data'));
  });
  
  return app;
};

describe('Resume Snapshot Service', () => {
  let app;
  
  beforeEach(() => {
    app = createTestApp();
  });
  
  describe('GET /health', () => {
    it('应该返回健康状态', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);
        
      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('service', 'resume-snapshot');
      expect(response.body).toHaveProperty('timestamp');
    });
  });
  
  describe('GET /', () => {
    it('应该返回服务信息', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);
        
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('service', 'resume-snapshot');
      expect(response.body).toHaveProperty('version');
    });
  });
  
  describe('GET /ping', () => {
    it('应该返回 pong', async () => {
      const response = await request(app)
        .get('/ping')
        .expect(200);
        
      expect(response.body).toHaveProperty('message', 'pong');
    });
  });
  
  describe('POST /resume-snapshot', () => {
    it('应该成功生成截图', async () => {
      const html = '<html><body><h1>测试简历</h1></body></html>';
      
      const response = await request(app)
        .post('/resume-snapshot')
        .send({ html })
        .expect(200);
        
      expect(response.headers['content-type']).toBe('image/png');
    });
    
    it('缺少HTML时应该返回400错误', async () => {
      const response = await request(app)
        .post('/resume-snapshot')
        .send({})
        .expect(400);
        
      expect(response.body).toHaveProperty('error');
    });
    
    it('应该支持自定义选项', async () => {
      const html = '<html><body><h1>测试简历</h1></body></html>';
      const options = {
        width: 800,
        height: 1000,
        format: 'jpeg'
      };
      
      await request(app)
        .post('/resume-snapshot')
        .send({ html, options })
        .expect(200);
    });
  });
  
  describe('错误处理', () => {
    it('不存在的路径应该返回404', async () => {
      await request(app)
        .get('/nonexistent')
        .expect(404);
    });
  });
});

// 集成测试（需要真实的 Puppeteer）
describe('Integration Tests', () => {
  // 这些测试需要在有 Puppeteer 的环境中运行
  it.skip('应该能够生成真实的截图', async () => {
    // 这里可以添加真实的 Puppeteer 测试
  });
});

// 性能测试
describe('Performance Tests', () => {
  it.skip('应该在合理时间内响应', async () => {
    const app = createTestApp();
    const start = Date.now();
    
    await request(app)
      .get('/health')
      .expect(200);
      
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(1000); // 应该在1秒内响应
  });
});
