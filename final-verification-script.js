/**
 * 最终验证脚本 - 验证所有问题已彻底解决
 * 测试单个积木保存和批量积木保存的一致性
 */

console.log('🎯 最终验证脚本 - 验证积木数据持久化问题彻底解决');
console.log('🔧 主要修复：删除重复的addBrick方法，确保数据一致性');

async function runFinalVerification() {
  console.log('🚀 开始最终验证测试...');
  console.log('=' .repeat(60));
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    overall: 'FAIL'
  };
  
  try {
    // 获取BrickManager实例
    let BrickManager;
    try {
      const app = getApp();
      if (app && app.brickManager) {
        BrickManager = app.brickManager;
        console.log('✅ 通过app.brickManager获取BrickManager');
      } else {
        const brickManagerModule = require('../../utils/brick-manager.js');
        BrickManager = brickManagerModule.instance;
        console.log('✅ 通过require导入BrickManager');
      }
    } catch (error) {
      console.error('❌ 无法获取BrickManager:', error);
      throw new Error('BrickManager加载失败');
    }
    
    // 测试1: 重新初始化，获取基线数据
    console.log('\n🧪 测试1: 重新初始化，获取基线数据');
    BrickManager.initialized = false;
    BrickManager.bricks = [];
    await BrickManager.init();
    
    const baselineBricks = await BrickManager.getBricks();
    console.log(`📊 基线积木数量: ${baselineBricks.length}`);
    
    results.tests.baseline = {
      success: true,
      count: baselineBricks.length
    };
    
    // 测试2: 单个积木保存测试（修复后）
    console.log('\n🧪 测试2: 单个积木保存测试（修复后）');
    const singleTestBrick = {
      id: 'final_single_' + Date.now(),
      title: '最终单个测试积木',
      content: '验证修复后的单个积木保存功能',
      category: 'skill',
      tags: ['最终测试', '单个保存'],
      createTime: new Date().toISOString()
    };
    
    try {
      console.log('🔄 测试单个积木保存（应该保存到云数据库）...');
      const addResult = await BrickManager.addBrick(singleTestBrick);
      console.log('✅ 单个积木保存成功:', addResult.id);
      
      // 验证是否保存到云数据库
      console.log('🔄 重新从云数据库加载，验证单个积木是否保存...');
      BrickManager.initialized = false;
      BrickManager.bricks = [];
      const afterSingleAdd = await BrickManager.getBricks();
      
      const hasSingleBrick = afterSingleAdd.some(brick => brick.id === singleTestBrick.id);
      
      results.tests.singleAdd = {
        success: hasSingleBrick,
        beforeCount: baselineBricks.length,
        afterCount: afterSingleAdd.length,
        foundInCloud: hasSingleBrick
      };
      
      console.log(`📊 单个保存后积木数量: ${afterSingleAdd.length}`);
      console.log(`🔍 在云数据库中找到单个积木: ${hasSingleBrick ? '✅ 是' : '❌ 否'}`);
      console.log(`🎯 单个积木保存: ${hasSingleBrick ? '✅ 通过' : '❌ 失败'}`);
      
    } catch (error) {
      console.error('❌ 单个积木保存失败:', error);
      results.tests.singleAdd = {
        success: false,
        error: error.message
      };
    }
    
    // 测试3: 批量保存测试
    console.log('\n🧪 测试3: 批量保存测试');
    const batchTestBricks = [
      {
        id: 'final_batch_1_' + Date.now(),
        title: '最终批量测试积木1',
        content: '验证批量保存功能',
        category: 'skill',
        tags: ['最终测试', '批量保存'],
        createTime: new Date().toISOString()
      },
      {
        id: 'final_batch_2_' + Date.now(),
        title: '最终批量测试积木2',
        content: '验证数据一致性',
        category: 'project',
        tags: ['数据一致性', '批量保存'],
        createTime: new Date().toISOString()
      }
    ];
    
    try {
      const beforeBatch = await BrickManager.getBricks();
      console.log(`📊 批量保存前积木数量: ${beforeBatch.length}`);
      
      console.log('🔄 开始批量保存测试...');
      const batchResult = await BrickManager.addBricksBatch(batchTestBricks);
      
      console.log('📊 批量保存结果:', {
        success: batchResult.success,
        totalCount: batchResult.totalCount,
        addedCount: batchResult.addedCount,
        cloudSaveSuccess: batchResult.cloudSaveSuccess
      });
      
      // 验证批量保存到云数据库
      console.log('🔄 重新从云数据库加载，验证批量积木是否保存...');
      BrickManager.initialized = false;
      BrickManager.bricks = [];
      const afterBatch = await BrickManager.getBricks();
      
      const hasAllBatchBricks = batchTestBricks.every(testBrick => 
        afterBatch.some(brick => brick.id === testBrick.id)
      );
      
      results.tests.batchSave = {
        success: batchResult.success && batchResult.cloudSaveSuccess && hasAllBatchBricks,
        beforeCount: beforeBatch.length,
        afterCount: afterBatch.length,
        addedCount: batchResult.addedCount,
        cloudSaveSuccess: batchResult.cloudSaveSuccess,
        hasAllBricks: hasAllBatchBricks
      };
      
      console.log(`📊 批量保存后积木数量: ${afterBatch.length}`);
      console.log(`🔍 在云数据库中找到所有批量积木: ${hasAllBatchBricks ? '✅ 是' : '❌ 否'}`);
      console.log(`☁️ 云数据库保存: ${batchResult.cloudSaveSuccess ? '✅ 成功' : '❌ 失败'}`);
      console.log(`🎯 批量保存测试: ${results.tests.batchSave.success ? '✅ 通过' : '❌ 失败'}`);
      
    } catch (error) {
      console.error('❌ 批量保存测试失败:', error);
      results.tests.batchSave = {
        success: false,
        error: error.message
      };
    }
    
    // 测试4: 数据一致性验证
    console.log('\n🧪 测试4: 数据一致性验证');
    try {
      const currentBricks = await BrickManager.getBricks();
      console.log(`📊 当前积木数量: ${currentBricks.length}`);
      
      // 检查所有测试积木是否都在云数据库中
      const allTestBricks = [singleTestBrick, ...batchTestBricks];
      const allTestBricksInCloud = allTestBricks.every(testBrick => 
        currentBricks.some(brick => brick.id === testBrick.id)
      );
      
      // 模拟页面切换
      console.log('🔄 模拟页面切换，验证数据一致性...');
      BrickManager.initialized = false;
      BrickManager.bricks = [];
      const afterSwitch = await BrickManager.getBricks();
      
      const dataConsistent = currentBricks.length === afterSwitch.length;
      const testBricksStillThere = allTestBricks.every(testBrick => 
        afterSwitch.some(brick => brick.id === testBrick.id)
      );
      
      results.tests.consistency = {
        success: dataConsistent && allTestBricksInCloud && testBricksStillThere,
        beforeSwitch: currentBricks.length,
        afterSwitch: afterSwitch.length,
        dataConsistent,
        allTestBricksInCloud,
        testBricksStillThere
      };
      
      console.log(`📊 页面切换前: ${currentBricks.length}, 切换后: ${afterSwitch.length}`);
      console.log(`🔍 数据数量一致: ${dataConsistent ? '✅ 是' : '❌ 否'}`);
      console.log(`🔍 所有测试积木在云数据库: ${allTestBricksInCloud ? '✅ 是' : '❌ 否'}`);
      console.log(`🔍 页面切换后测试积木仍存在: ${testBricksStillThere ? '✅ 是' : '❌ 否'}`);
      console.log(`🎯 数据一致性: ${results.tests.consistency.success ? '✅ 通过' : '❌ 失败'}`);
      
    } catch (error) {
      console.error('❌ 数据一致性测试失败:', error);
      results.tests.consistency = {
        success: false,
        error: error.message
      };
    }
    
    // 测试5: 最终完整性验证
    console.log('\n🧪 测试5: 最终完整性验证');
    try {
      console.log('🔄 执行最终刷新...');
      await BrickManager.refresh();
      
      const finalBricks = await BrickManager.getBricks();
      console.log(`📊 最终积木数量: ${finalBricks.length}`);
      
      // 验证所有功能都正常
      const expectedMinimumCount = baselineBricks.length + 3; // 基线 + 1个单个 + 2个批量
      const hasMinimumCount = finalBricks.length >= expectedMinimumCount;
      
      // 验证测试积木完整性
      const allTestBricks = [singleTestBrick, ...batchTestBricks];
      const allTestBricksPresent = allTestBricks.every(testBrick => 
        finalBricks.some(brick => brick.id === testBrick.id)
      );
      
      results.tests.finalIntegrity = {
        success: hasMinimumCount && allTestBricksPresent,
        finalCount: finalBricks.length,
        expectedMinimum: expectedMinimumCount,
        hasMinimumCount,
        allTestBricksPresent
      };
      
      console.log(`📊 期望最少积木数量: ${expectedMinimumCount}, 实际: ${finalBricks.length}`);
      console.log(`🔍 达到最少数量: ${hasMinimumCount ? '✅ 是' : '❌ 否'}`);
      console.log(`🔍 所有测试积木存在: ${allTestBricksPresent ? '✅ 是' : '❌ 否'}`);
      console.log(`🎯 最终完整性: ${results.tests.finalIntegrity.success ? '✅ 通过' : '❌ 失败'}`);
      
    } catch (error) {
      console.error('❌ 最终完整性测试失败:', error);
      results.tests.finalIntegrity = {
        success: false,
        error: error.message
      };
    }
    
    // 计算总体结果
    const testCount = Object.keys(results.tests).length;
    const successCount = Object.values(results.tests).filter(test => test.success).length;
    const successRate = (successCount / testCount) * 100;
    results.overall = successRate >= 90 ? 'PASS' : 'FAIL'; // 5个测试中至少4.5个通过
    
    console.log('\n' + '=' .repeat(60));
    console.log('📋 最终验证结果汇总');
    console.log(`🎯 总体结果: ${results.overall}`);
    console.log(`📊 成功率: ${successRate.toFixed(1)}%`);
    console.log(`✅ 通过测试: ${successCount}/${testCount}`);
    
    // 详细结果
    console.log('\n📋 详细测试结果:');
    const testNameMap = {
      baseline: '基线数据获取',
      singleAdd: '单个积木保存',
      batchSave: '批量积木保存',
      consistency: '数据一致性',
      finalIntegrity: '最终完整性'
    };
    
    Object.entries(results.tests).forEach(([testName, result], index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${testNameMap[testName]}: ${result.success ? '通过' : '失败'}`);
      if (!result.success && result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    if (results.overall === 'PASS') {
      console.log('\n🎉 🎉 🎉 恭喜！积木数据持久化问题已彻底解决！🎉 🎉 🎉');
      console.log('✅ 单个积木保存功能正常');
      console.log('✅ 批量积木保存功能正常');
      console.log('✅ 云数据库保存功能正常');
      console.log('✅ 数据在页面切换后保持一致');
      console.log('✅ 所有数据持久化功能正常');
      console.log('\n🚀 现在bricks.js解析的积木能被generate.js正确获取了！');
      console.log('🎊 积木数据持久化问题已彻底解决！');
      
      // 显示最终统计
      const finalBricks = await BrickManager.getBricks();
      console.log(`\n📊 最终积木统计: ${finalBricks.length}个积木`);
      console.log('🎯 原始问题：bricks.js解析21个积木 → generate.js获取0个积木');
      console.log('✅ 解决后：bricks.js解析积木 → 正确保存到云数据库 → generate.js正确获取');
      
    } else {
      console.log('\n⚠️ 部分测试失败，需要进一步检查');
      console.log('🔍 请查看详细测试结果');
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ 最终验证测试执行失败:', error);
    results.error = error.message;
    results.overall = 'ERROR';
    return results;
  }
}

// 执行最终验证
console.log('🎯 准备执行最终验证测试...');
runFinalVerification().then(result => {
  console.log('\n🏁 最终验证测试执行完成！');
  
  if (result.overall === 'PASS') {
    console.log('🎊 积木数据持久化问题已彻底解决！');
    console.log('🚀 您现在可以正常使用简历生成功能了！');
    console.log('✅ 问题解决确认：bricks.js解析的积木现在能被generate.js正确获取');
  } else if (result.overall === 'ERROR') {
    console.log('🔧 测试执行遇到错误，请检查环境配置。');
  } else {
    console.log('🔧 部分功能需要进一步优化。');
  }
}).catch(error => {
  console.error('💥 测试执行异常:', error);
});

console.log('\n📖 使用说明:');
console.log('1. 确保在微信开发者工具中运行');
console.log('2. 复制此脚本到控制台执行');
console.log('3. 查看测试结果，验证所有问题已解决');
console.log('4. 如果测试通过，积木数据持久化问题已彻底解决');
