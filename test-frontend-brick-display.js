/**
 * 前端积木库页面显示测试脚本
 * 模拟微信小程序环境中的积木数据加载和显示
 */

// 模拟微信小程序环境
const mockWxEnvironment = {
  // 模拟用户登录状态
  userInfo: {
    openid: 'o16hGvi6bsSwrpnbtFIg06wVutX4',
    nickName: '开发者测试用户',
    avatarUrl: 'https://example.com/avatar.jpg'
  },
  
  // 模拟云开发环境
  cloud: {
    callFunction: async (options) => {
      console.log('🔧 模拟云函数调用:', options.name, options.data);
      
      if (options.name === 'brickManager' && options.data.action === 'list') {
        // 模拟返回用户的积木数据
        return {
          result: {
            success: true,
            data: {
              bricks: [
                {
                  id: 'personal_1753880576935',
                  title: '个人信息概览',
                  description: '王五，产品经理，联系方式13700137000，邮箱******************。',
                  category: 'personal',
                  keywords: ['个人信息', '联系方式', '王五'],
                  confidence: 1,
                  usageCount: 0,
                  level: '基础',
                  createTime: '2025-07-30T13:03:08.071Z',
                  updateTime: '2025-07-30T13:03:08.071Z',
                  source: 'resume_upload',
                  enhancedByAI: true
                },
                {
                  id: 'education_1753880576935_0',
                  title: '交大本科学历',
                  description: '上海交通大学本科毕业，具备扎实的学术基础。',
                  category: 'education',
                  keywords: ['教育背景', '本科', '上海交通大学'],
                  confidence: 0.9,
                  usageCount: 0,
                  level: '基础',
                  createTime: '2025-07-30T13:03:08.073Z',
                  updateTime: '2025-07-30T13:03:08.073Z',
                  source: 'resume_upload',
                  enhancedByAI: true
                },
                {
                  id: 'work_basic_1753880576935_0',
                  title: '字节跳动产品经理',
                  description: '2019-2024年任职字节跳动，负责产品管理。',
                  category: 'experience',
                  keywords: ['字节跳动', '产品经理', '工作经历'],
                  confidence: 0.9,
                  usageCount: 0,
                  level: '基础',
                  createTime: '2025-07-30T13:03:08.074Z',
                  updateTime: '2025-07-30T13:03:08.074Z',
                  source: 'resume_upload',
                  enhancedByAI: true
                },
                {
                  id: 'project_1753880576935_0_0',
                  title: '抖音功能设计',
                  description: '主导抖音产品功能设计，提升用户体验。',
                  category: 'project',
                  keywords: ['产品', '专业能力', '团队协作', '问题解决'],
                  confidence: 0.85,
                  usageCount: 0,
                  level: '基础',
                  createTime: '2025-07-30T13:03:08.074Z',
                  updateTime: '2025-07-30T13:03:08.074Z',
                  source: 'resume_upload',
                  enhancedByAI: true
                },
                {
                  id: 'project_1753880576936_0_1',
                  title: '产品路线管理',
                  description: '管理产品路线，精准对接用户需求。',
                  category: 'project',
                  keywords: ['管理', '产品', '团队协作', '问题解决'],
                  confidence: 0.85,
                  usageCount: 0,
                  level: '基础',
                  createTime: '2025-07-30T13:03:08.074Z',
                  updateTime: '2025-07-30T13:03:08.074Z',
                  source: 'resume_upload',
                  enhancedByAI: true
                }
              ]
            }
          }
        };
      }
      
      return { result: { success: false, error: '未知的云函数调用' } };
    }
  }
};

/**
 * 模拟积木库页面的数据加载逻辑
 */
class MockBrickLibraryPage {
  constructor() {
    this.data = {
      bricks: [],
      filteredBricks: [],
      loading: false,
      totalCount: 0,
      personalCount: 0,
      educationCount: 0,
      experienceCount: 0,
      projectCount: 0
    };
  }

  /**
   * 模拟页面加载
   */
  async onLoad() {
    console.log('📱 模拟积木库页面加载...');
    await this.loadBricksList();
  }

  /**
   * 模拟积木数据加载
   */
  async loadBricksList() {
    console.log('🔄 开始加载积木列表...');
    this.setData({ loading: true });

    try {
      // 模拟调用 brickManager 云函数
      const result = await mockWxEnvironment.cloud.callFunction({
        name: 'brickManager',
        data: {
          action: 'list',
          data: {
            limit: 100,
            sortBy: 'updateTime',
            sortOrder: 'desc'
          }
        }
      });

      if (result.result.success) {
        const bricks = result.result.data.bricks;
        console.log(`✅ 成功获取 ${bricks.length} 个积木`);

        // 处理积木数据
        const processedBricks = bricks.map(brick => ({
          ...brick,
          selected: false,
          icon: this.getBrickIcon(brick.category),
          matchCount: brick.usageCount || 0,
          createTime: this.formatDate(brick.createTime),
          updateTime: this.formatDate(brick.updateTime)
        }));

        this.setData({
          bricks: processedBricks,
          loading: false
        });

        // 更新统计和筛选
        this.updateCounts();
        this.filterBricks();

        console.log('✅ 积木数据加载完成');
        this.displayBricksSummary();
      } else {
        console.error('❌ 积木数据加载失败:', result.result.error);
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('❌ 积木数据加载异常:', error);
      this.setData({ loading: false });
    }
  }

  /**
   * 模拟 setData 方法
   */
  setData(data) {
    Object.assign(this.data, data);
  }

  /**
   * 获取积木图标
   */
  getBrickIcon(category) {
    const iconMap = {
      personal: '👤',
      education: '🎓',
      experience: '💼',
      project: '🚀',
      skill: '💻'
    };
    return iconMap[category] || '📋';
  }

  /**
   * 格式化日期
   */
  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
  }

  /**
   * 更新统计数据
   */
  updateCounts() {
    const bricks = this.data.bricks;
    const counts = {
      totalCount: bricks.length,
      personalCount: bricks.filter(b => b.category === 'personal').length,
      educationCount: bricks.filter(b => b.category === 'education').length,
      experienceCount: bricks.filter(b => b.category === 'experience').length,
      projectCount: bricks.filter(b => b.category === 'project').length
    };
    
    this.setData(counts);
    console.log('📊 积木统计更新:', counts);
  }

  /**
   * 筛选积木
   */
  filterBricks() {
    this.setData({
      filteredBricks: this.data.bricks
    });
  }

  /**
   * 显示积木摘要
   */
  displayBricksSummary() {
    console.log('\n📋 积木库数据摘要:');
    console.log(`总积木数: ${this.data.totalCount}`);
    console.log(`个人信息: ${this.data.personalCount} 个`);
    console.log(`教育背景: ${this.data.educationCount} 个`);
    console.log(`工作经历: ${this.data.experienceCount} 个`);
    console.log(`项目能力: ${this.data.projectCount} 个`);
    
    console.log('\n🔍 积木详情:');
    this.data.bricks.forEach((brick, index) => {
      console.log(`${index + 1}. ${brick.icon} ${brick.title} (${brick.category})`);
      console.log(`   描述: ${brick.description}`);
      console.log(`   创建时间: ${brick.createTime}`);
    });
  }
}

/**
 * 运行测试
 */
async function runFrontendBrickDisplayTest() {
  console.log('🧪 开始前端积木库页面显示测试...\n');
  
  // 创建模拟页面实例
  const brickPage = new MockBrickLibraryPage();
  
  // 模拟页面加载
  await brickPage.onLoad();
  
  console.log('\n🎉 前端积木库页面显示测试完成！');
  console.log('✅ 积木数据加载正常');
  console.log('✅ 数据统计功能正常');
  console.log('✅ 页面显示逻辑正常');
}

// 运行测试
if (require.main === module) {
  runFrontendBrickDisplayTest().then(() => {
    console.log('\n🏁 测试脚本执行完成');
  }).catch(error => {
    console.error('\n💥 测试脚本执行失败:', error);
  });
}

module.exports = {
  MockBrickLibraryPage,
  runFrontendBrickDisplayTest,
  mockWxEnvironment
};
