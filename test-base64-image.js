/**
 * 测试base64图片数据的脚本
 */

const fs = require('fs');

// 从云函数返回结果中提取base64数据进行测试
async function testBase64Image() {
    try {
        console.log('🧪 开始测试base64图片数据...');
        
        // 调用云函数获取结果
        const cloud = require('wx-server-sdk');
        cloud.init({
            env: 'zemuresume-4gjvx1wea78e3d1e'
        });
        
        const result = await cloud.callFunction({
            name: 'resumePreviewGenerator',
            data: {
                format: 'png',
                resumeData: {
                    personalInfo: {
                        name: 'Base64测试用户',
                        email: '<EMAIL>',
                        phone: '13800138007'
                    },
                    summary: '这是一个base64图片数据测试。',
                    workExperience: [{
                        position: 'Base64测试工程师',
                        company: 'Base64测试公司',
                        duration: '2024.11 - 至今'
                    }],
                    skills: ['Base64测试技能']
                }
            }
        });
        
        console.log('✅ 云函数调用成功');
        
        if (result.result && result.result.statusCode === 200) {
            const data = JSON.parse(result.result.body);
            
            if (data.success && data.data.png && data.data.png.imageData) {
                const base64Data = data.data.png.imageData;
                console.log('📊 Base64数据长度:', base64Data.length);
                
                // 将base64数据保存为PNG文件
                const imageBuffer = Buffer.from(base64Data, 'base64');
                const outputPath = '/tmp/base64-test-image.png';
                
                fs.writeFileSync(outputPath, imageBuffer);
                console.log('💾 Base64图片已保存到:', outputPath);
                
                // 验证文件
                const stats = fs.statSync(outputPath);
                console.log('📏 文件大小:', stats.size, 'bytes');
                
                if (stats.size > 0) {
                    console.log('🎉 Base64图片数据测试成功！');
                    return true;
                } else {
                    console.log('❌ Base64图片文件为空');
                    return false;
                }
            } else {
                console.log('❌ 云函数返回结果中没有base64图片数据');
                console.log('返回数据:', JSON.stringify(data, null, 2));
                return false;
            }
        } else {
            console.log('❌ 云函数调用失败');
            console.log('结果:', result);
            return false;
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return false;
    }
}

// 运行测试
if (require.main === module) {
    testBase64Image().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testBase64Image };
