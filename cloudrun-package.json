{"name": "resume-snapshot", "version": "1.0.0", "description": "AI简历系统微信云托管截图服务 - Resume snapshot service for generating PNG and PDF previews", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "lint": "eslint .", "docker:build": "docker build -t resume-snapshot .", "docker:run": "docker run -p 80:80 resume-snapshot"}, "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "cors": "^2.8.5", "express": "^4.18.0", "helmet": "^7.0.0", "puppeteer": "^21.0.0", "express-rate-limit": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.0", "jest": "^29.0.0", "eslint": "^8.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["resume", "snapshot", "pdf", "png", "puppeteer", "cloudbase", "wechat", "cloudrun"], "author": "AI Resume System", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xuyuzeamazon/ai-resume-cloudrun.git"}}