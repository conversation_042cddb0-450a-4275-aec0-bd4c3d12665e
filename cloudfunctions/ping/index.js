/**
 * 云函数：ping
 * 用于测试云开发连接状态和AI模型调用
 *
 * 更新时间：2025年7月15日
 */

const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  try {
    console.log('📡 收到ping请求:', event)

    // 处理身份验证相关操作
    if (event.action === 'auth-check' || event.action === 'establish-auth') {
      console.log('🔐 处理身份验证请求:', event.action)

      const authResponse = {
        success: !!wxContext.OPENID,
        message: wxContext.OPENID ? 'auth-success' : 'auth-failed',
        timestamp: new Date().toISOString(),
        environment: cloud.DYNAMIC_CURRENT_ENV,
        openid: wxContext.OPENID,
        appid: wxContext.APPID,
        unionid: wxContext.UNIONID,
        requestId: context.requestId,
        action: event.action
      }

      console.log('🔐 身份验证响应:', {
        success: authResponse.success,
        hasOpenid: !!wxContext.OPENID,
        action: event.action
      })

      return authResponse
    }

    // 如果请求包含AI测试参数，则测试AI模型调用
    if (event.testAI) {
      const aiTestResult = await testDeepSeekModel(event.testPrompt || '你好，请简单介绍一下自己')

      return {
        success: true,
        message: 'pong with AI test',
        timestamp: new Date().toISOString(),
        environment: cloud.DYNAMIC_CURRENT_ENV,
        openid: wxContext.OPENID,
        appid: wxContext.APPID,
        unionid: wxContext.UNIONID,
        requestId: context.requestId,
        aiTest: aiTestResult,
        data: event
      }
    }

    // 返回基本的连接信息
    const response = {
      success: true,
      message: 'pong',
      timestamp: new Date().toISOString(),
      environment: cloud.DYNAMIC_CURRENT_ENV,
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID,
      requestId: context.requestId,
      data: event
    }

    console.log('✅ ping响应:', response)
    return response

  } catch (error) {
    console.error('❌ ping处理失败:', error)

    return {
      success: false,
      message: 'ping failed',
      error: error.message,
      timestamp: new Date().toISOString(),
      requestId: context.requestId
    }
  }
}

/**
 * 测试 DeepSeek 模型调用
 * @param {string} prompt - 测试提示词
 * @returns {Object} 测试结果
 */
async function testDeepSeekModel(prompt) {
  try {
    console.log('🤖 开始测试DeepSeek模型调用')

    // 创建 DeepSeek 模型实例
    const model = cloud.AI.createModel("deepseek")

    // 调用模型进行文本生成
    const res = await model.streamText({
      data: {
        model: "deepseek-v3",
        messages: [
          { role: "system", content: "你是一个专业的AI助手，请简洁明了地回答问题。" },
          { role: "user", content: prompt }
        ]
      }
    })

    // 收集流式响应
    let fullResponse = ''
    for await (let str of res.textStream) {
      fullResponse += str
    }

    console.log('✅ DeepSeek模型调用成功')

    return {
      success: true,
      response: fullResponse,
      model: "deepseek-v3",
      timestamp: new Date().toISOString()
    }

  } catch (error) {
    console.error('❌ DeepSeek模型调用失败:', error)

    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
}
